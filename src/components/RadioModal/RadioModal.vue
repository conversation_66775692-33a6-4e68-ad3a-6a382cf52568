<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="请选择" @ok="handleSubmit" @cancel="handleClose"
    :bodyStyle="{ minHeight: '400px !important' }">
    <BasicTable v-if="props.columns.length > 0" @register="registerTable" ref="tableRef"
      :row-selection="{ selectedRowKeys: innerSelectedKeys, type: 'radio', onChange: onSelectChange }">
      <template #toolbar> </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, reactive, provide, Ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { BasicTable, useTable, TableAction, ActionItem, BasicColumn, FormSchema } from '/@/components/Table';
import { getTemplate } from '/@/api/bdm/contract';
import { useUserStore } from '/@/store/modules/user';
const { notification } = useMessage();
const isCopy = ref<boolean>(false);
const state = reactive({
  formModel: {},
  rowId: '',
  record: {},
});
const props = defineProps<{
  columns: BasicColumn[];
  api: (...args: any[]) => Promise<any>;
    showSearchForm: Boolean;
}>()
provide<Ref<boolean>>('isCopy', isCopy);
const selectedKeys = ref<string[]>([]);
const emits = defineEmits(['success', 'register']);
const { t } = useI18n();

const innerSelectedKeys = ref<string[]>([]);
const selectedRowsData = ref<any[]>([]);
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  // console.log(data.columns[0])
  // selectedKeys.value = [];
  // // columns.value=data.columns
  setModalProps({
    confirmLoading: false,
    destroyOnClose: true,
    maskClosable: false,
    showCancelBtn: true,
    showOkBtn: true,
    canFullscreen: true,
    width: 1100,
  });
  state.rowId = data.id;
  state.record = data.record;
});
async function handleSubmit() {
  setModalProps({ confirmLoading: true });
  try {
    console.log(selectedRowsData.value)
    if (selectedRowsData.value.length < 1) {
      return notification.error({
        message: 'Tip',
        description: '请选择数据',
      });
    }
    console.log(innerSelectedKeys.value)
    emits('success', innerSelectedKeys.value,selectedRowsData.value);
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
function handleClose() {
}



function onSelectChange(selectedRowKeys: [], selectedRows) {
  innerSelectedKeys.value = selectedRowKeys
  selectedRowsData.value = selectedRows;
}



const searchFormSchema: FormSchema[] = [
  {
    field: 'name ',
    label: '名称',
    component: 'Input',
  },
];
const userStore = useUserStore();

const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  title: '',
  api: props.api,
  rowKey: 'id',
  // immediate:false,
  columns: props.columns,
  formConfig: {
    rowProps: {
      gutter: 10,
    },
    schemas: searchFormSchema,
    fieldMapToTime: [],
    showResetButton: false,
  },
  beforeFetch: (params) => {
    clearSelectedRowKeys()
    return { ...params, PK: 'id', userId: userStore.getUserInfo.id };
  },
  afterFetch: () => {
  },
  useSearchForm: props.showSearchForm,
  showTableSetting: false,

  striped: false,
  tableSetting: {
    size: true,
    setting: false,
  },
});

</script>
