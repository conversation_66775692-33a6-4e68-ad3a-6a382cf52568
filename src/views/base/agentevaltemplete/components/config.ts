import { Tag } from 'ant-design-vue';
import { FormProps, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { t } from '/@/hooks/web/useI18n';
import { h } from 'vue';
import { getYears } from '/@/api/base/agentevaltemplete';
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '模板名称',
    component: 'Input',
  },
];

export const columns: BasicColumn[] = [
  {
    resizable: true,
    dataIndex: 'code',
    title: '模板编号',
    componentType: 'auto-code',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'name',
    title: '模板名称',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'year',
    title: '年度',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: '',
  },
  {
    resizable: true,
    dataIndex: 'partnershipStatus',
    title: '评估对象',
    componentType: 'input',
    sorter: true,
    customRender: ({ record }) => {
      const partnershipStatus = record.partnershipStatus;
      const text = ~~partnershipStatus === 1 ? t('代理商') : t('储备代理商');
      return text;
    },
    styleConfig: undefined,
    listStyle: '',
  },

  {
    dataIndex: 'enabledMark',
    title: '状态',
    sorter: true,
    customRender: ({ record }) => {
      const enabledMark = record.enabledMark;
      const enable = ~~enabledMark === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? t('启用') : t('禁用');
      return h(Tag, { color: color }, () => text);
    },
  },
];
//表单事件
export const formEventConfigs = {
  0: [
    {
      type: 'circle',
      color: '#2774ff',
      text: '开始节点',
      icon: '#icon-kaishi',
      bgcColor: '#D8E5FF',
      isUserDefined: false,
    },
    {
      color: '#F6AB01',
      icon: '#icon-chushihua',
      text: '初始化表单',
      bgcColor: '#f9f5ea',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  1: [
    {
      color: '#B36EDB',
      icon: '#icon-shujufenxi',
      text: '获取表单数据',
      detail: '(新增无此操作)',
      bgcColor: '#F8F2FC',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  2: [
    {
      color: '#F8625C',
      icon: '#icon-jiazai',
      text: '加载表单',
      bgcColor: '#FFF1F1',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  3: [
    {
      color: '#6C6AE0',
      icon: '#icon-jsontijiao',
      text: '提交表单',
      bgcColor: '#F5F4FF',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  4: [
    {
      type: 'circle',
      color: '#F8625C',
      text: '结束节点',
      icon: '#icon-jieshuzhiliao',
      bgcColor: '#FFD6D6',
      isLast: true,
      isUserDefined: false,
    },
  ],
};
export const formProps: FormProps = {
  labelCol: { span: 3, offset: 0 },
  labelAlign: 'right',
  layout: 'vertical',
  size: 'default',
  rowProps: { gutter: 30 }, //整体行间距
  schemas: [
    {
      key: 'd6016b264b96493f947341d7e7ee9dgh',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '基本信息',
      componentProps: {
        defaultValue: '基本信息',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: 'b5dc7e0ed4274bbc8ff9b1573fe6e06e',
      field: 'name',
      label: '模板名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入模板名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'dde22ca5a0184ee18f4b94e9cc24bbd8',
      field: 'code',
      label: '模板编号',
      type: 'auto-code',
      component: 'AutoCodeRule',
      colProps: { span: 12 },
      componentProps: {
        width: '100%',
        span: '',
        placeholder: '请输入模板编号',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        showLabel: true,
        autoCodeRule: 'agentEvalCode',
        required: false,
        isShow: true,
        style: { width: '100%' },
      },
    },
    {
      key: '01302e9f4ec44f4caed901f7cc7836a3',
      field: 'year',
      label: '年度',
      type: 'select',
      component: 'ApiSelect',
      defaultValue: [],
      colProps: { span: 12 },
      componentProps: () => {
        return {
          width: '100%',
          span: '15',
          placeholder: '请选择年度',
          showLabel: true,
          showSearch: true,
          disabled: false,
          api: getYears,
          labelField: 'label',
          valueField: 'value',
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: '01302e9f4ec44f4caed901f7cc7836a3',
      field: 'partnershipStatus',
      label: '评估对象',
      type: 'select',
      component: 'Select',
      colProps: { span: 12 },
      defaultValue: [],
      componentProps: () => {
        return {
          width: '100%',
          placeholder: '请选择评估对象',
          showLabel: true,
          showSearch: true,
          disabled: false,
          options: [
            { value: 1, label: '代理商' },
            { value: 2, label: '储备代理商' },
          ],
          labelField: 'label',
          valueField: 'value',
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: '3183e0b914a44b18995c37c5dd5dfbdd',
      field: 'remark',
      label: '备注',
      type: 'textarea',
      component: 'InputTextArea',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入备注',
        maxlength: 255,
        rows: 4,
        autoSize: false,
        showCount: true,
        disabled: false,
        showLabel: true,
        allowClear: false,
        required: false,
        isShow: true,
        rules: [],
        events: {},
        style: { width: '100%' },
      },
    },
    {
      key: 'c7f37c6ac90d4e4d8923f05f7db7c70c',
      field: 'enabledMark',
      label: '状态',
      type: 'switch',
      component: 'Switch',
      colProps: { span: 24 },
      defaultValue: 1,
      componentProps: {
        span: '',
        defaultValue: 1,
        checkedChildren: '',
        unCheckedChildren: '',
        checkedColor: '#5e95ff',
        unCheckedColor: '#bbbdbf',
        showLabel: true,
        disabled: false,
        events: {},
        isShow: false,
        style: {},
      },
    },
    {
      key: '406bf3db3bc84305baea5788568ce323',
      field: 'id',
      label: 'id',
      type: 'input',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入id',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'd6016b264b96493f947341d7e7ee9ddr',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '评估指标',
      componentProps: {
        defaultValue: '评估指标',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: '6e83b0d728b4460188757f241b60217f',
      field: 'bdmAgentEvalTmplIndRelList',
      label: '',
      slot: 'bdmAgentEvalTmplIndRelList',
      component: 'Slot',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '24',
        defaultValue: '',
        placeholder: '',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: true,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    // {
    //   key: '3da9267f0d7448c0835cd589d73c5a8a',
    //   label: '',
    //   field: 'bdmAgentEvalTmplIndRelList',
    //   type: 'form',
    //   component: 'SubForm',
    //   required: true,
    //   colProps: { span: 24 },
    //   componentProps: {
    //     mainKey: 'bdmAgentEvalTmplIndRelList',
    //     columns: [
    //       {
    //         key: '022f046477b94765ae51f6c04401e26e',
    //         title: '评估指标',
    //         dataIndex: 'pingGuZhiBiao2314',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入评估指标',
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: true,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: true,
    //           isShow: true,
    //           scan: false,
    //           prestrainField: 'label',
    //         },
    //       },
    //       {
    //         key: '95ebb05603f0424b9b3d12f5a5db2eed',
    //         title: '评估维度',
    //         dataIndex: 'danXingWenBen1516',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入评估维度',
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: true,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: true,
    //           isShow: true,
    //           scan: false,
    //           prestrainField: 'typeName',
    //         },
    //       },
    //       {
    //         key: '3b52546fc17d4f729b4b20558ea99522',
    //         title: '维度排序',
    //         dataIndex: 'typeSort',
    //         componentType: 'InputNumber',
    //         defaultValue: 0,
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: 0,
    //           min: 0,
    //           max: 100,
    //           step: 1,
    //           disabled: false,
    //           showLabel: true,
    //           controls: true,
    //           required: false,
    //           subTotal: false,
    //           isShow: true,
    //           rules: [],
    //           events: {},
    //         },
    //       },
    //       {
    //         key: 'd9f96dd0c4f9425d98a63609719291fc',
    //         title: 'templete_id',
    //         dataIndex: 'templateId',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入templete_id',
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: 'c47b029caa464b98b5c44b7cf409dd30',
    //         title: 'indicator_id',
    //         dataIndex: 'indicatorId',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入indicator_id',
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //           prestrainField: 'value',
    //         },
    //       },
    //       {
    //         key: '0cfb06ee78564f1990d150cf8d505519',
    //         title: 'id',
    //         dataIndex: 'id',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入id',
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       { title: '操作', key: 'action', fixed: 'right', width: '50px' },
    //     ],
    //     span: '24',
    //     preloadType: 'api',
    //     apiConfig: {
    //       path: 'common/getAgentEvalIndicatorSelect',
    //       method: 'GET',
    //       apiId: '7149c65bdf5649c585e97912c6eee0dd',
    //       apiParams: [
    //         { key: '1', title: 'Query Params', tableInfo: [] },
    //         { key: '2', title: 'Header', tableInfo: [] },
    //         { key: '3', title: 'Body' },
    //       ],
    //       script:
    //         "var sql = 'select id as value ,name as label,type_name as typeName,type_code as typeCode from bdm_agent_eval_indicator where delete_mark=0 and enabled_mark=1?{keyword, and name =#{keyword}}';\r\nreturn db.select(sql);",
    //       outputParams: [
    //         { name: 'label', tableTitle: '评估指标' },
    //         { name: 'typeName', tableTitle: '评估维度' },
    //       ],
    //     },
    //     itemId: '',
    //     dicOptions: [],
    //     useSelectButton: true,
    //     buttonName: '选择数据',
    //     showLabel: true,
    //     showComponentBorder: true,
    //     showFormBorder: true,
    //     showIndex: false,
    //     isShow: true,
    //     multipleHeads: [],
    //     isExport: false,
    //     isImport: false,
    //     isListView: false,
    //     viewList: [],
    //   },
    // },
  ],
  showActionButtonGroup: false,
  buttonLocation: 'center',
  actionColOptions: { span: 24 },
  showResetButton: false,
  showSubmitButton: false,
  hiddenComponent: [],
};

//左侧树结构配置
export const treeConfig = {
  id: '',
  isMultiple: false,
  name: '',
  type: 1,
  configTip: '',
  config: [],
};
