<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="导入记录">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'ant-design:eye-outlined',
              onClick: handleView.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
  <FileimportModal @register="registerFileModal" />
</template>
<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicTable, useTable, FormSchema, BasicColumn, TableAction } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getSysFileImportPage } from '/@/api/system/fileimport';
  import FileimportModal from '../../../system/fileimport/components/FileimportModal.vue';
  const { t } = useI18n();
  const emit = defineEmits(['success', 'register']);
  const { notification } = useMessage();
  const isView = ref(false);
  const state = reactive({
    menuCode: '',
  });
  const columns: BasicColumn[] = [
    {
      resizable: true,
      dataIndex: 'code',
      title: '编码',
      componentType: 'input',

      sorter: true,

      styleConfig: undefined,
      listStyle: '',
    },

    {
      resizable: true,
      dataIndex: 'name',
      title: '名称',
      componentType: 'input',

      sorter: true,

      styleConfig: undefined,
      listStyle: '',
    },

    {
      resizable: true,
      dataIndex: 'menuName',
      title: '功能名称',
      componentType: 'input',

      sorter: true,

      styleConfig: undefined,
      listStyle: '',
    },

    {
      resizable: true,
      dataIndex: 'exportUserName',
      title: '导入人',
      componentType: 'input',

      sorter: true,

      styleConfig: undefined,
      listStyle: '',
    },

    {
      resizable: true,
      dataIndex: 'finishTime',
      title: '导入完成时间',
      componentType: 'date',

      sorter: true,

      styleConfig: undefined,
      listStyle: undefined,
    },

    {
      resizable: true,
      dataIndex: 'importStatus',
      title: '导入状态',
      componentType: 'select',

      sorter: true,

      styleConfig: undefined,
      listStyle: undefined,
    },

    {
      resizable: true,
      dataIndex: 'menuCode',
      title: '功能编码',
      componentType: 'input',

      sorter: true,

      styleConfig: undefined,
      listStyle: '',
    },
  ];

  // const searchFormSchema: FormSchema[] = [
  //   {
  //     field: 'name',
  //     label: '终端客户名称',
  //     component: 'Input',
  //     componentProps: {
  //       placeholder: '请输入终端客户名称',
  //     }
  //   },
  // ];
  const [registerFileModal, { openModal }] = useModal();
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    state.menuCode = data.menuCode;
    setModalProps({
      confirmLoading: false,
      showCancelBtn: false,
      showOkBtn: false,
      destroyOnClose: true,
      width: 1100,
    });
  });
  const [registerTable, { setTableData }] = useTable({
    title: '',
    columns,
    rowKey: 'id',
    api: getSysFileImportPage,
    bordered: true,
    canResize: false,
    showIndexColumn: false,
    useSearchForm: false,
    showTableSetting: true,
    clearSelectOnPageChange: true,
    formConfig: {
      // rowProps: {
      //   gutter: 16,
      // },
      // schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: true,
      // submitButtonOptions: {
      //   onClick: () => {
      //     clearSelectedRowKeys();
      //   },
      // },
    },

    pagination: {
      pageSize: 10,
    },
    tableSetting: {
      size: false,
      setting: false,
    },
    // rowSelection: {
    //   type: 'radio',
    //   columnWidth: 50,
    // },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    beforeFetch: (params) => {
      //发送请求默认新增
      return { ...params, menuCode: state.menuCode || -1 };
    },
  });

  function handleView(record) {
    openModal(true, {
      isView: true,
      id: record.id,
    });
  }
</script>
