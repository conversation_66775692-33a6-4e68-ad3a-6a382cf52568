<template>
  <ResizePageWrapper :hasLeft="false">
    <template #resizeRight>
      <BasicTable
        @register="registerTable"
        isMenuTable
        ref="tableRef"
        :row-selection="{ selectedRowKeys: selectedKeys, onChange: onSelectChange }"
      >
        <template #toolbar>
          <template v-for="button in tableButtonConfig" :key="button.code">
            <a-button
              v-if="button.isDefault"
              type="primary"
              v-auth="`agent:${button.code}`"
              @click="buttonClick(button.code)"
            >
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
            <a-button v-else type="primary">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.componentType === 'switch'">
            <a-switch
              v-model:checked="record[column.dataIndex]"
              :unCheckedValue="0"
              :checkedValue="1"
              :disabled="true"
            />
          </template>
          <template v-if="column.dataIndex === 'idType'">
            <span v-if="record.type === '个人'">中国大陆居民身份证</span>
            <span v-if="record.type === '企业'">企业统一社会信用代码</span>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="getActions(record)" />
          </template>

          <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
          </template>
          <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
              record[column.dataIndex]
            }}</span>
          </template>
        </template>
      </BasicTable>
    </template>

    <AgentModal @register="registerModal" @success="handleSuccess" />

    <ImportModal
      @register="registerImportModal"
      importUrl="/bdm/agent/import"
      @success="handleImportSuccess"
    />
    <ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="columns"
      @success="handleExportSuccess"
    />
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import { ref, computed, createVNode } from 'vue';

  import { Modal } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
  import {
    getBdmAgentPage,
    deleteBdmAgent,
    enableList,
    disableList,
    agentSyncList,
    exportBdmAgent,
  } from '/@/api/bdm/agent';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { executeListStyle } from '/@/hooks/web/useListStyle'; //列表样式配置
  import { useRouter } from 'vue-router';

  import { useModal } from '/@/components/Modal';

  import AgentModal from './components/AgentModal.vue';

  import { ImportModal } from '/@/components/Import';

  import { searchFormSchema, columns } from './components/config';

  import Icon from '/@/components/Icon/index';

  import { downloadByData } from '/@/utils/file/download';

  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  const visibleExport = ref(false);

  const filterColumns = filterColumnAuth(columns);
  const tableRef = ref();
  const pageParamsInfo = ref<any>({});

  //选择框
  const selectedKeys = ref<string[]>([]);
  const selectedRowsData = ref<any[]>([]);
  //展示在列表内的按钮
  const actionButtons = ref<string[]>([
    'view',
    'edit',
    'copyData',
    'delete',
    'startwork',
    'flowRecord',
    'pushorder',
  ]);
  const buttonConfigs = computed(() => {
    const list = [
      {
        buttonId: '1866730249937731584',
        name: '查看',
        code: 'view',
        icon: 'ant-design:eye-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1866730249941925888',
        name: '新增',
        code: 'add',
        icon: 'ant-design:plus-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1866730249941925889',
        name: '编辑',
        code: 'edit',
        icon: 'ant-design:form-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1866730249941925890',
        name: '快速导入',
        code: 'import',
        icon: 'ant-design:import-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        isUse: true,
        name: '快速导出',
        code: 'export',
        icon: 'ant-design:export-outlined',
        isDefault: true,
      },
      {
        buttonId: '1866730249941925891',
        name: '删除',
        code: 'delete',
        icon: 'ant-design:delete-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1868611264788316162',
        isUse: true,
        name: '禁用',
        code: 'disable',
        icon: 'ant-design:lock-filled',
        isDefault: true,
      },
      {
        buttonId: '1868611264788316161',
        name: '启用',
        code: 'enable',
        icon: 'ant-design:unlock-filled',
        isDefault: true,
        isUse: true,
      },
      {
        isUse: true,
        name: '同步',
        code: 'sync',
        icon: 'ant-design:sync-outlined',
        isDefault: true,
      },
    ];
    return filterButtonAuth(list);
  });

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const btnEvent = {
    view: handleView,
    add: handleAdd,
    edit: handleEdit,
    import: handleImport,
    export: handleExport,
    delete: handleDelete,
    enable: handleEnable,
    disable: handleDisable,
    sync: handleSync,
  };

  const { currentRoute } = useRouter();

  const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

  const [registerModal, { openModal }] = useModal();

  const [registerImportModal, { openModal: openImportModal }] = useModal();

  const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
    title: '代理商客户列表',
    api: getBdmAgentPage,
    rowKey: 'id',
    columns: filterColumns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: false,
    },
    beforeFetch: (params) => {
      clearSelectedRowKeys();
      pageParamsInfo.value = {
        ...params,
        FormId: formIdComputedRef.value,
        PK: 'id',
        partnershipStatus: 1,
      };
      return pageParamsInfo.value;
    },
    afterFetch: (res) => {
      tableRef.value.setToolBarWidth();
    },
    useSearchForm: true,
    showTableSetting: true,

    striped: false,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow,
    isAdvancedQuery: false,
    querySelectOption: JSON.stringify(searchFormSchema),
    objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
  });

  function buttonClick(code) {
    btnEvent[code]();
  }

  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteList([record.id]);
  }

  function deleteList(ids) {
    Modal.confirm({
      title: '提示信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认删除？',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        deleteBdmAgent(ids).then((_) => {
          handleSuccess();
          notification.success({
            message: 'Tip',
            description: t('删除成功！'),
          });
        });
      },
      onCancel() {},
    });
  }

  function customRow(record: Recordable) {
    return {
      ondblclick: () => {
        if (record.isCanEdit && hasPermission('agent:edit')) {
          handleEdit(record);
        }
      },
    };
  }

  function handleSuccess() {
    selectedKeys.value = [];
    selectedRowsData.value = [];
    clearSelectedRowKeys();
    reload();
  }

  function handleView(record: Recordable) {
    openModal(true, {
      isView: true,
      id: record.id,
    });
  }

  async function handleExport() {
    //visibleExport.value = true;
    handleExportSuccess('');
  }
  async function handleExportSuccess(cols) {
    const res = await exportBdmAgent({
      isTemplate: false,
      columns: cols.toString(),
      ...pageParamsInfo.value,
    });
    visibleExport.value = false;
    downloadByData(
      res.data,
      '代理商客户.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  function handleImport() {
    openImportModal(true, {
      title: '快速导入',
      templateTitle: '代理商客户模板',
      downLoadUrl: '/bdm/agent/export',
    });
  }
  function handleImportSuccess() {
    reload();
  }

  function getActions(record: Recordable): ActionItem[] {
    record.isCanEdit = false;

    const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        return {
          icon: button?.icon,
          auth: `agent:${button.code}`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        if (button.code === 'view') {
          return {
            icon: button?.icon,
            auth: `agent:${button.code}`,
            tooltip: button?.name,
            onClick: btnEvent[button.code].bind(null, record),
          };
        } else {
          return {};
        }
      }
    });
    return actionsList;
  }
  //选择框
  function onSelectChange(selectedRowKeys: [], selectedRows) {
    selectedKeys.value = selectedRowKeys;
    selectedRowsData.value = selectedRows;
  }

  function handleEnable() {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要启用的数据'),
      });
      return;
    }
    enableKolList(selectedKeys.value);
  }

  function enableKolList(ids) {
    Modal.confirm({
      title: '提示信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认启用？',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        enableList(ids).then((_) => {
          handleSuccess();
          notification.success({
            message: 'Tip',
            description: t('启用成功！'),
          });
        });
      },
      onCancel() {},
    });
  }

  function handleDisable() {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要禁用的数据'),
      });
      return;
    }
    disableKolList(selectedKeys.value);
  }

  function disableKolList(ids) {
    Modal.confirm({
      title: '提示信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认禁用？',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        disableList(ids).then((_) => {
          handleSuccess();
          notification.success({
            message: 'Tip',
            description: t('禁用成功！'),
          });
        });
      },
      onCancel() {},
    });
  }
  async function handleSync() {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要同步的数据'),
      });
      return;
    }
    if (selectedKeys.value.length == 1) {
      if (selectedRowsData.value[0].syncStatus == 2) {
        notification.warning({
          message: 'Tip',
          description: t('已同步，不需要再次同步'),
        });
        return;
      }
    }
    await agentSyncList(selectedKeys.value)
      .then((res) => {
        handleSuccess();
        if (res == 'ok') {
          notification.success({
            message: 'Tip',
            description: t('数据同步中稍后刷新页面！'),
          });
        } else {
          notification.warning({
            message: 'Tip',
            description: t(res),
          });
        }
      })
      .catch((_) => {
        handleSuccess();
      });
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }
  .show {
    display: flex;
  }
  .hide {
    display: none !important;
  }
</style>
