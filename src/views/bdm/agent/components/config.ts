import { FormProps, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { getIdCard, getBusinessLicense } from '/@/api/bdm/agent';
import { t } from '/@/hooks/web/useI18n';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getAreaProvinceList } from '/@/api/system/area';

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '代理商名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入代理商名称',
    },
  },
  {
    field: 'type',
    label: '代理商类型',
    component: 'XjrSelect',
    componentProps: {
      placeholder: '请选择代理商类型',
      datasourceType: 'dic',
      params: { itemId: '1866686246655471618' },
      labelField: 'name',
      valueField: 'value',

      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'userName',
    label: '招商经理',
    component: 'Input',
    componentProps: {
      placeholder: '请输入招商经理名称',
    },
  },
  {
    field: 'province',
    label: '所在省份',
    component: 'ApiSelect',
    componentProps: () => {
      return {
        placeholder: '请选择所在省份',
        showSearch: true,
        api: getAreaProvinceList,
        labelField: 'name',
        valueField: 'code',
        getPopupContainer: () => document.body,
      };
    },
  },
  // {
  //   field: 'partnershipStatus',
  //   label: '合作状态',
  //   component: 'XjrSelect',
  //   componentProps: {
  //     datasourceType: 'dic',
  //     params: { itemId: '1866690675077132289' },
  //     labelField: 'name',
  //     valueField: 'value',

  //     getPopupContainer: () => document.body,
  //   },
  // },

  {
    field: 'peopleNumber',
    label: '团队人员数量',
    component: 'XjrSelect',
    componentProps: {
      placeholder: '请选择团队人员数量',
      datasourceType: 'dic',
      params: { itemId: '1866692077019049985' },
      labelField: 'name',
      valueField: 'value',

      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'fiscalCompetence',
    label: '财税能力',
    component: 'XjrSelect',
    componentProps: {
      placeholder: '请选择财税能力',
      datasourceType: 'dic',
      params: { itemId: '1866711273920638978' },
      labelField: 'name',
      valueField: 'value',

      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'enabledMark',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
  },
];

export const columns: BasicColumn[] = [
  {
    resizable: true,
    dataIndex: 'name',
    title: '代理商名称',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'code',
    title: '代理商编码',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'type',
    title: '代理商类型',
    componentType: 'select',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'idType',
    title: '代理商证件类型',
    componentType: 'select',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'provinceName',
    title: '所在省份',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'primaryProvinceName',
    title: '主要覆盖省份',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },
  {
    resizable: true,
    dataIndex: 'peopleNumber',
    title: '团队人员数量',
    componentType: 'select',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'fiscalCompetence',
    title: '财税能力',
    componentType: 'select',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'syncStatus',
    title: '同步合同系统状态',
    componentType: 'input',

    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '未推送', value: 1 },
        { key: 2, label: '已推送', value: 2 },
        { key: 3, label: '推送失败', value: 3 },
      ];

      return staticOptions.filter((x) => x.value === record.syncStatus)[0]?.label;
    },

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    dataIndex: 'enabledMark',
    title: '状态',
    sorter: true,
    customRender: ({ record }) => {
      const enabledMark = record.enabledMark;
      const enable = ~~enabledMark === 1;
      const color = enable ? 'green' : 'red';
      const text = enable ? t('启用') : t('禁用');
      return h(Tag, { color: color }, () => text);
    },
  },
];
//表单事件
export const formEventConfigs = {
  0: [
    {
      type: 'circle',
      color: '#2774ff',
      text: '开始节点',
      icon: '#icon-kaishi',
      bgcColor: '#D8E5FF',
      isUserDefined: false,
    },
    {
      color: '#F6AB01',
      icon: '#icon-chushihua',
      text: '初始化表单',
      bgcColor: '#f9f5ea',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  1: [
    {
      color: '#B36EDB',
      icon: '#icon-shujufenxi',
      text: '获取表单数据',
      detail: '(新增无此操作)',
      bgcColor: '#F8F2FC',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  2: [
    {
      color: '#F8625C',
      icon: '#icon-jiazai',
      text: '加载表单',
      bgcColor: '#FFF1F1',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  3: [
    {
      color: '#6C6AE0',
      icon: '#icon-jsontijiao',
      text: '提交表单',
      bgcColor: '#F5F4FF',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  4: [
    {
      type: 'circle',
      color: '#F8625C',
      text: '结束节点',
      icon: '#icon-jieshuzhiliao',
      bgcColor: '#FFD6D6',
      isLast: true,
      isUserDefined: false,
    },
  ],
};
export const formProps: FormProps = {
  labelCol: { span: 20, offset: 0 },
  labelAlign: 'right',
  layout: 'vertical',
  size: 'default',
  rowProps: {
    gutter: 30,
  },
  schemas: [
    {
      key: 'd4bc88a3f2ab47c0ad2aaa211a870d7b',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '附件信息',
      componentProps: {
        defaultValue: '附件信息',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: '9a5007eac3aa4fd5b9f24aa98032484f',
      field: 'type',
      label: '代理商类型',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel, formActionType }) => {
        return {
          onChange: (e, option) => {
            formModel.idType = e;
            formModel.typeName = option?.label;
            formModel.name = '';
            formModel.endDate = '';
            formModel.idNumber = '';
            formModel.personIdNumber = '';
            formModel.address = '';
            formModel.businessLicense = '';
            formModel.accountOpeningPermit = '';
            formModel.idCardNegative = '';
            formModel.idCardPositive = '';
            if (e === '2') {
              formModel.idTypeName = '身份证号码';
              formModel.contactPerson = formModel.name;
              formModel.personIdNumber = formModel.idNumber;
              formActionType.updateSchema([
                {
                  field: 'contactPerson',
                  dynamicDisabled: true,
                },
                {
                  field: 'personIdNumber',
                  dynamicDisabled: true,
                },
              ]);
            } else {
              formModel.idTypeName = '企业统一社会信用代码';
              formModel.contactPerson = '';
              formModel.personIdNumber = '';
              formActionType.updateSchema([
                {
                  field: 'contactPerson',
                  dynamicDisabled: false,
                },
                {
                  field: 'personIdNumber',
                  dynamicDisabled: false,
                },
              ]);
            }
          },
          width: '100%',
          span: '',
          placeholder: '请选择代理商类型',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: false,
          defaultSelect: null,
          datasourceType: 'dic',
          params: { itemId: '1866686246655471618' },
          labelField: 'name',
          valueField: 'value',
          apiConfig: {
            path: 'CodeGeneration/selection',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          itemId: '1866686246655471618',
          style: { width: '100%' },
        };
      },
    },
    {
      key: '',
      field: '',
      label: '',
      type: 'title',
      component: 'Title',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: {
        defaultValue: '',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: false,
        style: {},
      },
    },
    {
      key: '5c7fd77661724f41a0ab922362aeac31',
      field: 'businessLicense',
      label: '营业执照',
      type: 'Upload',
      component: 'Upload',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange: async (e) => {
            if (e) {
              let res = await getBusinessLicense({ url: e[0].fileUrl });
              formModel.name = res.name;
              formModel.idNumber = res.socialCreditCode;
              formModel.address = res.address;
              formModel.endDate = res.validityPeriod;
            } else {
            }
          },
          defaultValue: '',
          showLabel: true,
          limit: 1,
          maxNumber: 1,
          isUpload: true,
          events: {},
          style: {},
          accept: '.png,.jpg,.jpeg,.bmp',
          listType: 'picture-card',
        };
      },
      ifShow: (value) => {
        if (value.model.type == '1') {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      key: '80e8d38bfa07449bb6f7e7632e480309',
      field: 'accountOpeningPermit',
      label: '开户许可证',
      type: 'Upload',
      component: 'Upload',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: {
        span: '',
        defaultValue: '',
        showLabel: true,
        limit: 1,
        maxNumber: 1,
        isUpload: true,
        events: {},
        style: {},
        accept: '.png,.jpg,.jpeg,.bmp',
        listType: 'picture-card',
      },
      ifShow: (value) => {
        if (value.model.type == '1') {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      key: '84bf6a7e5fb847b48f27558435d9d47f',
      field: 'idCardPositive',
      label: '身份证正面（国徽）',
      type: 'Upload',
      component: 'Upload',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange: async (e) => {
            if (e) {
              try {
                let res = await getIdCard({ type: '2', url: e[0].fileUrl });
                formModel.endDate = res.endDate;
              } catch (error) {}
            } else {
            }
          },
          span: '',
          defaultValue: '',
          showLabel: true,
          maxNumber: 1,
          // isShow: true,
          isUpload: true,
          events: {},
          style: {},
          accept: '.png,.jpg,.jpeg,.bmp',
          listType: 'picture-card',
        };
      },
      ifShow: (value) => {
        if (value.model.type == '2') {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      key: '577ab2c8dfdb4d30a728a80c64551490',
      field: 'idCardNegative',
      label: '身份证反面(头像)',
      type: 'Upload',
      component: 'Upload',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange: async (e) => {
            if (e) {
              let res = await getIdCard({ type: '1', url: e[0].fileUrl });
              formModel.name = res.name;
              formModel.contactPerson = res.name;
              formModel.personIdNumber = res.idCardNumber;
              formModel.idNumber = res.idCardNumber;
              formModel.address = res.address;
            } else {
            }
          },
          span: '',
          defaultValue: '',
          showLabel: true,
          maxNumber: 1,
          // isShow: true,
          isUpload: true,
          events: {},
          style: {},
          accept: '.png,.jpg,.jpeg,.bmp',
          listType: 'picture-card',
        };
      },
      ifShow: (value) => {
        if (value.model.type == '2') {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      key: 'd4bc88a3f2ab47c0ad2aaa211a870d7a',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '基本信息',
      componentProps: {
        defaultValue: '基本信息',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: 'f44f5ba59111409196ef7971f4ef0e65',
      field: 'name',
      label: '代理商名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange: (e) => {
            if (formModel.type === '2') {
              formModel.contactPerson = formModel.name;
            }
          },
          width: '100%',
          span: '',
          defaultValue: '',
          placeholder: '请输入代理商名称',
          prefix: '',
          suffix: '',
          addonBefore: '',
          addonAfter: '',
          disabled: false,
          allowClear: false,
          showLabel: true,
          required: true,
          rules: [],
          events: {},
          listStyle: '',
          isSave: false,
          isShow: true,
          scan: false,
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'f44f5ba59111409196ef7971f4ef0e68',
      field: 'endDate',
      label: '结束日期',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          width: '100%',
          defaultValue: '',
          placeholder: '',
          disabled: false,
          allowClear: false,
          showLabel: true,
          rules: [],
          events: {},
          listStyle: '',
          isSave: false,
          isShow: false,
          scan: false,
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'e713319994244835865361b841a25a21',
      field: 'code',
      label: '代理商编号',
      type: 'auto-code',
      component: 'AutoCodeRule',
      colProps: { span: 8 },
      componentProps: {
        width: '100%',
        span: '',
        placeholder: '请输入代理商编号',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        showLabel: true,
        autoCodeRule: 'agentCode',
        required: true,
        isShow: true,
        style: { width: '100%' },
      },
    },
    {
      key: '72409761d54c4c949db4e3bf47a711e8',
      field: 'typeName',
      label: '类型名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入类型名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '1b359084c0024d82975b51554402058a',
      field: 'idType',
      label: '代理商证件类型',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.idTypeName = option?.label;
          },
          width: '100%',
          span: '',
          placeholder: '请选择代理商证件类型',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: true,
          datasourceType: 'dic',
          params: { itemId: '1866688750906617857' },
          labelField: 'name',
          valueField: 'value',
          apiConfig: {
            path: 'CodeGeneration/selection',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          itemId: '1866688750906617857',
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'eeeea595a2b541778b3e5ad77b06847d',
      field: 'idTypeName',
      label: '证件类型名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入证件证件类型名称名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'c0a4128b1d0144da8fe610a26e4749ce',
      field: 'idNumber',
      label: '证件编号',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange: (e) => {
            if (formModel.type === '2') {
              formModel.personIdNumber = formModel.idNumber;
            }
          },
          width: '100%',
          span: '',
          defaultValue: '',
          placeholder: '请输入证件编号',
          prefix: '',
          suffix: '',
          addonBefore: '',
          addonAfter: '',
          disabled: false,
          allowClear: false,
          showLabel: true,
          required: true,
          rules: [],
          events: {},
          listStyle: '',
          isSave: false,
          isShow: true,
          scan: false,
          style: { width: '100%' },
        };
      },
    },
    // {
    //   key: '393f90e99240480eacd8afd4d8b31b63',
    //   field: 'level',
    //   label: '代理商级别',
    //   type: 'select',
    //   component: 'XjrSelect',
    //   colProps: { span: 8 },
    //   componentProps: ({ formModel }) => {
    //     return {
    //       onChange: (e, option) => {
    //         formModel.levelName = option?.label;
    //       },
    //       width: '100%',
    //       span: '',
    //       placeholder: '请选择代理商级别',
    //       showLabel: true,
    //       showSearch: false,
    //       isMultiple: false,
    //       clearable: false,
    //       disabled: false,
    //       datasourceType: 'dic',
    //       params: { itemId: '1866689579302629378' },
    //       labelField: 'name',
    //       valueField: 'value',
    //       apiConfig: {
    //         path: 'CodeGeneration/selection',
    //         method: 'GET',
    //         apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    //       },
    //       dicOptions: [],
    //       required: true,
    //       rules: [],
    //       events: {},
    //       isShow: true,
    //       itemId: '1866689579302629378',
    //       style: { width: '100%' },
    //     };
    //   },
    // },
    // {
    //   key: '187e6a5fd109419c835e1c3b44f69f79',
    //   field: 'levelName',
    //   label: '级别名称',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 0 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入级别名称',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: false,
    //     allowClear: false,
    //     showLabel: true,
    //     required: false,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: false,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    {
      key: '859cce4edef54fd6b4a37abb8a1804b8',
      field: 'contactPerson',
      label: '代理商联系人',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入代理商联系人',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '859cce4edef54fd6b4a37abb8a1804b9',
      field: 'personIdNumber',
      label: '联系人身份证号',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入联系人身份证号',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'dd5225de39aa46c295b8f5fca4909b00',
      field: 'phone',
      label: '联系人电话',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入联系人电话',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [{ pattern: '/^1[3-9]\\d{9}$/', message: '请输入正确的手机号' }],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'a4980d300b7a42bf91575c02858ad83d',
      field: 'province',
      label: '所在省份',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.provinceName = option?.label;
            formModel.city = [];
            formModel.cityName = '';
          },
          width: '100%',
          span: '',
          placeholder: '请选择所在省份',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: false,
          defaultSelect: '',
          datasourceType: 'api',
          labelField: 'label',
          valueField: 'value',
          apiConfig: {
            path: 'common/getProvince',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'bcfe9ce963164d5290dfe536cc8c08aa',
      field: 'provinceName',
      label: '省份名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入省份名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'ceb85ce265784fbba0695b7a838aeba7',
      field: 'city',
      label: '所在城市',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.cityName = option?.label;
          },
          width: '100%',
          placeholder: '请选择所在城市',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: false,
          defaultSelect: '',
          datasourceType: 'api',
          labelField: 'label',
          valueField: 'value',
          apiConfig: {
            path: 'common/getCityByProvinceCode',
            method: 'GET',
            apiId: 'a7b09e131ba7465d95f38e2a21f63eb2',
            apiParams: [
              {
                key: '1',
                title: 'Query Params',
                tableInfo: [
                  {
                    name: 'provinceCode',
                    value: formModel.province || -1,
                    required: true,
                    bindType: 'value',
                  },
                ],
              },
              { key: '2', title: 'Header', tableInfo: [] },
              { key: '3', title: 'Body' },
            ],
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: '14771f7ad4aa472ca0de1eab8bcde39b',
      field: 'cityName',
      label: '城市名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入城市名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '320db092478e4722a4b446496ceadcb6',
      field: 'address',
      label: '详细地址',
      type: 'input',
      component: 'Input',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入详细地址',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '40d615fcdefb4e85b12ffa03ca914075',
      field: 'primaryProvince',
      label: '主要覆盖省份',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.primaryProvinceName = option?.map((option) => option.label).join(',');
            formModel.primaryCity = [];
            formModel.primaryCityName = '';
          },
          width: '100%',
          span: '',
          placeholder: '请选择主要覆盖省份',
          showLabel: true,
          showSearch: false,
          isMultiple: true,
          clearable: false,
          disabled: false,
          defaultSelect: '',
          datasourceType: 'api',
          labelField: 'label',
          valueField: 'value',
          apiConfig: {
            path: 'common/getProvince',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: '20e1b8bd93914a3daa25f8a58e2b9b12',
      field: 'primaryProvinceName',
      label: '主要覆盖省份名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入主要覆盖省份名称名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'a4996618386c4e30bc7813278f94abf2',
      field: 'primaryCity',
      label: '主要覆盖城市',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.primaryCityName = option?.map((option) => option.label).join(',');
          },
          width: '100%',
          span: '',
          placeholder: '请选择主要覆盖城市',
          showLabel: true,
          showSearch: false,
          isMultiple: true,
          clearable: false,
          disabled: false,
          defaultSelect: '',
          datasourceType: 'api',
          labelField: 'label',
          valueField: 'value',
          apiConfig: {
            path: 'common/getCityByProvinceCodes',
            method: 'GET',
            apiId: '2f7c2171b1e64fe9b6bb337480e30d23',
            apiParams: [
              {
                key: '1',
                title: 'Query Params',
                tableInfo: [
                  {
                    name: 'provinceCodes',
                    value: formModel.primaryProvince || '',
                    required: true,
                    bindType: 'value',
                  },
                ],
              },
              { key: '2', title: 'Header', tableInfo: [] },
              { key: '3', title: 'Body' },
            ],
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'f9ab6bcc5b7644d6b9603e583433267e',
      field: 'primaryCityName',
      label: '主要覆盖城市名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入主要覆盖城市名称名称名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '2994fa772d574ba8b3de29acc3c4516d',
      field: 'peopleNumber',
      label: '团队人员数量',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.peopleNumberName = option?.label;
          },
          width: '100%',
          span: '',
          placeholder: '请选择团队人员数量',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: false,
          datasourceType: 'dic',
          params: { itemId: '1866692077019049985' },
          labelField: 'name',
          valueField: 'value',
          apiConfig: {
            path: 'CodeGeneration/selection',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          itemId: '1866692077019049985',
          style: { width: '100%' },
        };
      },
    },
    {
      key: '6b29632b17ec4d7b8735a41d5c4bc619',
      field: 'peopleNumberName',
      label: '团队人员数量名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入团队人员数量名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'dae7fec3e22c4833b06d1eb185efdf9d',
      field: 'fiscalCompetence',
      label: '财税能力',
      type: 'select',
      component: 'XjrSelect',
      colProps: { span: 8 },
      componentProps: ({ formModel }) => {
        return {
          onChange: (e, option) => {
            formModel.fiscalCompetenceName = option?.label;
          },
          width: '100%',
          span: '',
          placeholder: '请选择财税能力',
          showLabel: true,
          showSearch: false,
          isMultiple: false,
          clearable: false,
          disabled: false,
          datasourceType: 'dic',
          params: { itemId: '1866711273920638978' },
          labelField: 'name',
          valueField: 'value',
          apiConfig: {
            path: 'CodeGeneration/selection',
            method: 'GET',
            apiId: '93d735dcb7364a0f8102188ec4d77ac7',
          },
          dicOptions: [],
          required: true,
          rules: [],
          events: {},
          isShow: true,
          itemId: '1866711273920638978',
          style: { width: '100%' },
        };
      },
    },
    {
      key: 'd2f240ce8ccf44a3910fc61adef40a5f',
      field: 'fiscalCompetenceName',
      label: '财税能力名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入财税能力名称名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    // {
    //   key: 'c5701fa129724eedb8bd936af925dc06',
    //   field: 'settleAccountName',
    //   label: '结算账户名称',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入结算账户名称',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: false,
    //     allowClear: false,
    //     showLabel: true,
    //     required: false,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: '70a221343bf54d83a6c0f82d8d977785',
    //   field: 'settleCardNumber',
    //   label: '结算账户卡号',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入结算账户卡号',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: false,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: '089f9e2d502c485384c177ef09e8efb5',
    //   field: 'bankName',
    //   label: '开户银行',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入开户银行',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: false,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },

    // {
    //   key: '3fb2e9c945cf4656b823f8ec568e283d',
    //   field: '',
    //   label: '标题',
    //   type: 'title',
    //   component: 'Title',
    //   colProps: { span: 24 },
    //   defaultValue: '开票信息',
    //   componentProps: {
    //     defaultValue: '开票信息',
    //     color: '',
    //     align: 'left',
    //     fontSize: 18,
    //     isShow: true,
    //     style: {},
    //   },
    // },
    // {
    //   key: '6e83b0d728b4460188757f241b60217f',
    //   field: 'companyName',
    //   label: '公司名称',
    //   slot: 'companyName',
    //   component: 'Slot',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入公司名称',
    //     maxlength: null,
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },

    // {
    //   key: '152f4b5016af45adb71b1a6f7326c41e',
    //   field: 'taxNumber',
    //   label: '统一社会信用代码',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入统一社会信用代码',
    //     maxlength: null,
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: '089f9e2d502c485384c177ef09e8efb5',
    //   field: 'companyBankName',
    //   label: '开户银行',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入开户银行',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: '70a221343bf54d83a6c0f82d8d977785',
    //   field: 'companySettleCardNumber',
    //   label: '银行账户',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入银行账户',
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: true,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: '6e83b0d728b4460188757f241b60219f',
    //   field: 'companyId',
    //   label: '公司ID',
    //   type: 'input',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '公司ID',
    //     maxlength: null,
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: false,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: false,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
    // {
    //   key: 'ae3171f06c63466488fd6c5abafbe2c1',
    //   label: '代理商代表',
    //   field: 'bdmAgentEmployeeList',
    //   type: 'form',
    //   component: 'SubForm',
    //   required: true,
    //   colProps: { span: 24 },
    //   componentProps: {
    //     mainKey: 'bdmAgentEmployeeList',
    //     columns: [
    //       {
    //         key: '1915ed80cf754a12aafdd6ef07c400df',
    //         title: '代理商id',
    //         dataIndex: 'agentId',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入代理商id',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '41b0139a527f4957a0e450cd2311372a',
    //         title: '代理商名称',
    //         dataIndex: 'agentName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入代理商名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '2b06989b5aa64d14a257d4b458ab96a6',
    //         title: '姓名',
    //         dataIndex: 'name',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入姓名',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: true,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: 'fae5d481b62a4867b508f20d46192fec',
    //         title: '联系电话',
    //         dataIndex: 'phone',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入联系电话',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: true,
    //           rules: [{ pattern: '/^1[3-9]\\d{9}$/', message: '请输入正确的手机号' }],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '2c97eb8d2b334bf08eb50593b3e9af76',
    //         title: '代理产品',
    //         dataIndex: 'productId',
    //         componentType: 'XjrSelect',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           placeholder: '请选择代理产品',
    //           showLabel: true,
    //           showSearch: false,
    //           isMultiple: true,
    //           clearable: false,
    //           disabled: false,
    //           staticOptions: [
    //             { key: 1, label: 'Option 1', value: 'Option 1' },
    //             { key: 2, label: 'Option 2', value: 'Option 2' },
    //             { key: 3, label: 'Option 3', value: 'Option 3' },
    //           ],
    //           defaultSelect: '',
    //           datasourceType: 'api',
    //           params: null,
    //           labelField: 'label',
    //           valueField: 'value',
    //           apiConfig: {
    //             path: 'CodeGeneration/selection',
    //             method: 'GET',
    //             apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    //           },
    //           dicOptions: [],
    //           required: true,
    //           rules: [],
    //           events: {},
    //           isShow: true,
    //         },
    //       },
    //       {
    //         key: '5f23fcda325b437f80ac54aa1963efae',
    //         title: '产品名称',
    //         dataIndex: 'productName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入产品名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '8ec83f37dd064c249357e1d80cc80755',
    //         title: '身份证号',
    //         dataIndex: 'idNumber',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入身份证号',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: true,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: 'b55c7a48c78a41708373179bf123e0b4',
    //         title: '服务区域',
    //         dataIndex: 'serviceProvince',
    //         componentType: 'XjrSelect',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           placeholder: '请选择服务区域',
    //           showLabel: true,
    //           showSearch: false,
    //           isMultiple: true,
    //           clearable: false,
    //           disabled: false,
    //           staticOptions: [
    //             { key: 1, label: 'Option 1', value: 'Option 1' },
    //             { key: 2, label: 'Option 2', value: 'Option 2' },
    //             { key: 3, label: 'Option 3', value: 'Option 3' },
    //           ],
    //           defaultSelect: '',
    //           datasourceType: 'api',
    //           params: null,
    //           labelField: 'label',
    //           valueField: 'value',
    //           apiConfig: {
    //             path: 'CodeGeneration/selection',
    //             method: 'GET',
    //             apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    //           },
    //           dicOptions: [],
    //           required: true,
    //           rules: [],
    //           events: {},
    //           isShow: true,
    //         },
    //       },
    //       {
    //         key: '7a64a74c74d94e7d987bfc295ebe08d7',
    //         title: '服务区域名称',
    //         dataIndex: 'serviceProvinceName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入服务区域名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: false,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '9bc40480a77648488844d95bfc24239d',
    //         dataIndex: 'idCardPositive',
    //         title: '身份证正面（国徽）',
    //         componentType: 'Upload',
    //         componentProps: {
    //           api: '#{upload}#',
    //           span: '',
    //           defaultValue: [],
    //           accept: '',
    //           maxNumber: 1,
    //           maxSize: 5,
    //           showLabel: true,
    //           multiple: false,
    //           disabled: false,
    //           required: true,
    //           isShow: true,
    //           events: {},
    //           listType: 'text',
    //         },
    //       },
    //       {
    //         key: 'efa66a1bdd254b729e60f92ecc83c4da',
    //         dataIndex: 'idCardNegative',
    //         title: '身份证反面(头像)',
    //         componentType: 'Upload',
    //         componentProps: {
    //           api: '#{upload}#',
    //           span: '',
    //           defaultValue: [],
    //           accept: '',
    //           maxNumber: 1,
    //           maxSize: 5,
    //           showLabel: true,
    //           multiple: false,
    //           disabled: false,
    //           required: true,
    //           isShow: true,
    //           events: {},
    //           listType: 'text',
    //         },
    //       },
    //       {
    //         key: 'a96a08f4a8134c64922608b2f2444950',
    //         title: '状态',
    //         dataIndex: 'enabledMark',
    //         componentType: 'Switch',
    //         defaultValue: 1,
    //         componentProps: {
    //           span: '',
    //           defaultValue: 1,
    //           checkedChildren: '',
    //           unCheckedChildren: '',
    //           checkedColor: '#5e95ff',
    //           unCheckedColor: '#bbbdbf',
    //           showLabel: true,
    //           disabled: false,
    //           events: {},
    //           isShow: true,
    //         },
    //       },
    //       { title: '操作', key: 'action', fixed: 'right', width: '50px' },
    //     ],
    //     span: '24',
    //     preloadType: 'api',
    //     apiConfig: {},
    //     itemId: '',
    //     dicOptions: [],
    //     useSelectButton: false,
    //     buttonName: '选择数据',
    //     showLabel: true,
    //     showComponentBorder: true,
    //     showFormBorder: true,
    //     showIndex: false,
    //     isShow: true,
    //     multipleHeads: [],
    //     isExport: false,
    //     isImport: false,
    //     isListView: false,
    //     viewList: [],
    //   },
    // },
  ],
  showActionButtonGroup: false,
  buttonLocation: 'center',
  actionColOptions: { span: 24 },
  showResetButton: false,
  showSubmitButton: false,
  hiddenComponent: [],
};

//左侧树结构配置
export const treeConfig = {
  id: '',
  isMultiple: false,
  name: '',
  type: 1,
  configTip: '',
  config: [],
};
