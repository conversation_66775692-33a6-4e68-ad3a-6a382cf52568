export const permissionList = [
  {
    required: false,
    view: true,
    edit: false,
    disabled: true,
    isSaveTable: false,
    tableName: '',
    fieldName: '标题',
    fieldId: '',
    isSubTable: false,
    showChildren: true,
    type: 'title',
    key: 'd4bc88a3f2ab47c0ad2aaa211a870d7a',
    children: [],
    options: {},
    defaultValue: '基本信息',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商名称',
    fieldId: 'name',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'f44f5ba59111409196ef7971f4ef0e65',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商类型',
    fieldId: 'type',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: '9a5007eac3aa4fd5b9f24aa98032484f',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '类型名称',
    fieldId: 'typeName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '72409761d54c4c949db4e3bf47a711e8',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商证件类型',
    fieldId: 'idType',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: '1b359084c0024d82975b51554402058a',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '证件类型名称',
    fieldId: 'idTypeName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'eeeea595a2b541778b3e5ad77b06847d',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '证件编号',
    fieldId: 'idNumber',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'c0a4128b1d0144da8fe610a26e4749ce',
    children: [],
    options: {},
    defaultValue: '',
  },
  // {
  //   required: true,
  //   view: true,
  //   edit: true,
  //   disabled: false,
  //   isSaveTable: false,
  //   tableName: '',
  //   fieldName: '代理商级别',
  //   fieldId: 'level',
  //   isSubTable: false,
  //   showChildren: true,
  //   type: 'select',
  //   key: '393f90e99240480eacd8afd4d8b31b63',
  //   children: [],
  //   options: {},
  // },
  // {
  //   required: false,
  //   view: true,
  //   edit: true,
  //   disabled: false,
  //   isSaveTable: false,
  //   tableName: '',
  //   fieldName: '级别名称',
  //   fieldId: 'levelName',
  //   isSubTable: false,
  //   showChildren: true,
  //   type: 'input',
  //   key: '187e6a5fd109419c835e1c3b44f69f79',
  //   children: [],
  //   options: {},
  //   defaultValue: '',
  // },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商联系人',
    fieldId: 'contactPerson',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '859cce4edef54fd6b4a37abb8a1804b8',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '联系人电话',
    fieldId: 'phone',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'dd5225de39aa46c295b8f5fca4909b00',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '合作状态',
    fieldId: 'partnershipStatus',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: '88c6cfeb2e184962b7e151e6a795cc64',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '合作状态名称',
    fieldId: 'partnershipStatusName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'b0bcbed67e2844e6964e8eb6a53928c7',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '所在省份',
    fieldId: 'province',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: 'a4980d300b7a42bf91575c02858ad83d',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '省份名称',
    fieldId: 'provinceName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'bcfe9ce963164d5290dfe536cc8c08aa',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '所在城市',
    fieldId: 'city',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: 'ceb85ce265784fbba0695b7a838aeba7',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '城市名称',
    fieldId: 'cityName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '14771f7ad4aa472ca0de1eab8bcde39b',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '详细地址',
    fieldId: 'address',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '320db092478e4722a4b446496ceadcb6',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '主要覆盖省份',
    fieldId: 'primaryProvince',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: '40d615fcdefb4e85b12ffa03ca914075',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '主要覆盖省份名称',
    fieldId: 'primaryProvinceName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '20e1b8bd93914a3daa25f8a58e2b9b12',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '主要覆盖城市',
    fieldId: 'primaryCity',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: 'a4996618386c4e30bc7813278f94abf2',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '主要覆盖城市名称',
    fieldId: 'primaryCityName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'f9ab6bcc5b7644d6b9603e583433267e',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '团队人员数量',
    fieldId: 'peopleNumber',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: '2994fa772d574ba8b3de29acc3c4516d',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '团队人员数量名称',
    fieldId: 'peopleNumberName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '6b29632b17ec4d7b8735a41d5c4bc619',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '财税能力',
    fieldId: 'fiscalCompetence',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: 'dae7fec3e22c4833b06d1eb185efdf9d',
    children: [],
    options: {},
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: true,
    isSaveTable: false,
    tableName: '',
    fieldName: '合作状态',
    fieldId: 'partnershipStatus',
    isSubTable: false,
    showChildren: true,
    type: 'select',
    key: 'c5701fa129724eedb8bd936af925dc06',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '财税能力名称',
    fieldId: 'fiscalCompetenceName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'd2f240ce8ccf44a3910fc61adef40a5f',
    children: [],
    options: {},
    defaultValue: '',
  },
  // {
  //   required: true,
  //   view: true,
  //   edit: true,
  //   disabled: false,
  //   isSaveTable: false,
  //   tableName: '',
  //   fieldName: '结算账户名称',
  //   fieldId: 'settleAccountName',
  //   isSubTable: false,
  //   showChildren: true,
  //   type: 'input',
  //   key: 'c5701fa129724eedb8bd936af925dc06',
  //   children: [],
  //   options: {},
  //   defaultValue: '',
  // },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '结算账户卡号',
    fieldId: 'settleCardNumber',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '70a221343bf54d83a6c0f82d8d977785',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '开户银行',
    fieldId: 'bankName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '089f9e2d502c485384c177ef09e8efb5',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '营业执照',
    fieldId: 'businessLicense',
    isSubTable: false,
    showChildren: true,
    type: 'image',
    key: '5c7fd77661724f41a0ab922362aeac31',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '开户许可证',
    fieldId: 'accountOpeningPermit',
    isSubTable: false,
    showChildren: true,
    type: 'image',
    key: '80e8d38bfa07449bb6f7e7632e480309',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '身份证正面（国徽）',
    fieldId: 'idCardPositive',
    isSubTable: false,
    showChildren: true,
    type: 'image',
    key: '84bf6a7e5fb847b48f27558435d9d47f',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '身份证反面(头像)',
    fieldId: 'idCardNegative',
    isSubTable: false,
    showChildren: true,
    type: 'image',
    key: '577ab2c8dfdb4d30a728a80c64551490',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '状态',
    fieldId: 'enabledMark',
    isSubTable: false,
    showChildren: true,
    type: 'switch',
    key: '1d0d24544f9f4fa79869714065aaba89',
    children: [],
    options: {},
    defaultValue: 1,
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSubTable: true,
    showChildren: false,
    tableName: 'bdmAgentEmployeeList',
    fieldName: '代理商代表',
    fieldId: 'bdmAgentEmployeeList',
    type: 'form',
    key: 'ae3171f06c63466488fd6c5abafbe2c1',
    children: [
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '代理商id',
        fieldId: 'agentId',
        key: '1915ed80cf754a12aafdd6ef07c400df',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '代理商名称',
        fieldId: 'agentName',
        key: '41b0139a527f4957a0e450cd2311372a',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '姓名',
        fieldId: 'name',
        key: '2b06989b5aa64d14a257d4b458ab96a6',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '联系电话',
        fieldId: 'phone',
        key: 'fae5d481b62a4867b508f20d46192fec',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '代理产品',
        fieldId: 'productId',
        key: '2c97eb8d2b334bf08eb50593b3e9af76',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '产品名称',
        fieldId: 'productName',
        key: '5f23fcda325b437f80ac54aa1963efae',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '身份证号',
        fieldId: 'idNumber',
        key: '8ec83f37dd064c249357e1d80cc80755',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '服务区域',
        fieldId: 'serviceProvince',
        key: 'b55c7a48c78a41708373179bf123e0b4',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '服务区域名称',
        fieldId: 'serviceProvinceName',
        key: '7a64a74c74d94e7d987bfc295ebe08d7',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '身份证正面（国徽）',
        fieldId: 'idCardPositive',
        key: '9bc40480a77648488844d95bfc24239d',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '身份证反面(头像)',
        fieldId: 'idCardNegative',
        key: 'efa66a1bdd254b729e60f92ecc83c4da',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEmployeeList',
        fieldName: '状态',
        fieldId: 'enabledMark',
        key: 'a96a08f4a8134c64922608b2f2444950',
        children: [],
      },
    ],
  },
];
