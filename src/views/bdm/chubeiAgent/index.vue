<template>
  <div id="agentRepres">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.name"
          placeholder="代理商名称"
          allowClear
          @change="getList()"
          @press-enter="getList()"
        />
        <a-select
          v-model:value="searchForm.type"
          style="width: 240px"
          placeholder="代理商类型"
          :options="typeOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.userId"
          placeholder="创建人"
          allowClear
          @change="getList()"
          @press-enter="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button type="primary" @click="handleAdd()">新增</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #action="{ record }">
            <a-button
              v-if="hasPermission('chubeiAgent:edit')"
              type="link"
              @click.stop="onEdit(record)"
              >编辑</a-button
            >
            <a-button type="link" @click.stop="onDel(record)">删除</a-button>
            <a-button type="link" @click.stop="onView(record)">查看</a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <!-- 新增/编辑/查看弹窗 -->
  <a-modal
    v-model:visible="openSignVisible"
    :title="modalTitle"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="handleModalCancel"
    width="100%"
    wrap-class-name="full-modal-chubeiAgent"
    :footer="isViewMode ? null : undefined"
  >
    <div class="modal_box">
      <a-form
        class="form_box"
        ref="formRef"
        :model="modalInfo"
        :rules="formRules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 19 }"
        autocomplete="off"
        labelAlign="right"
        :colon="true"
      >
        <!-- 基础信息 -->
        <a-row>
          <a-col class="import-title">基础信息</a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="代理商名称" name="name">
              <a-input
                v-model:value="modalInfo.name"
                placeholder="请输入代理商名称"
                :disabled="isViewMode"
                maxlength="200"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="代理商编号" name="code">
              <a-input v-model:value="modalInfo.code" disabled placeholder="" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="代理商类型" name="type">
              <a-select
                v-model:value="modalInfo.type"
                placeholder="请选择代理商类型"
                :options="typeOptions"
                :field-names="{ label: 'name', value: 'value' }"
                :disabled="isViewMode"
                @change="changeType"
                allowClear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="联系电话" name="phone">
              <a-input
                v-model:value="modalInfo.phone"
                placeholder="请输入联系电话"
                :disabled="isViewMode"
                maxlength="11"
                @blur="phoneBlur"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              :label="modalInfo.type === '1' ? '企业统一社会信用代码' : '身份证号码'"
              name="idNumber"
            >
              <a-input
                v-model:value="modalInfo.idNumber"
                placeholder=""
                :disabled="isViewMode"
                maxlength="200"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="团队人员" name="peopleNumber">
              <a-select
                v-model:value="modalInfo.peopleNumber"
                placeholder="请选择团队人员"
                :options="teamOptions"
                :field-names="{ label: 'name', value: 'value' }"
                :disabled="isViewMode"
                @change="changePeopleNumber"
                allowClear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="所在省份" name="province">
              <a-select
                v-model:value="modalInfo.province"
                placeholder="请选择所在省份"
                :options="provinceOptions"
                :field-names="{ label: 'label', value: 'value' }"
                :disabled="isViewMode"
                @change="onProvinceChange"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所在城市" name="city">
              <a-select
                v-model:value="modalInfo.city"
                placeholder="请选择所在城市"
                :options="cityOptions"
                :field-names="{ label: 'label', value: 'value' }"
                :disabled="isViewMode || !modalInfo.province"
                :loading="cityLoading"
                @change="changeCity"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="详细地址" name="address">
              <a-input
                v-model:value="modalInfo.address"
                placeholder=""
                :disabled="isViewMode"
                maxlength="200"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="主要覆盖省份" name="primaryProvince">
              <a-select
                v-model:value="modalInfo.primaryProvince"
                placeholder="请选择省份"
                :options="provinceOptions"
                :field-names="{ label: 'label', value: 'value' }"
                :disabled="isViewMode"
                @change="onPrimaryProvinceChange"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="主要覆盖城市" name="primaryCity">
              <a-select
                v-model:value="modalInfo.primaryCity"
                placeholder="请选择城市"
                :options="primaryCityOptions"
                :field-names="{ label: 'label', value: 'value' }"
                :disabled="isViewMode || !modalInfo.primaryProvince"
                :loading="primaryCityLoading"
                @change="changePrimaryCity"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="财税能力" name="fiscalCompetence">
              <a-select
                v-model:value="modalInfo.fiscalCompetence"
                placeholder="请选择财税能力"
                :options="fiscalOptions"
                :field-names="{ label: 'name', value: 'value' }"
                :disabled="isViewMode"
                @change="changeFiscal"
                allowClear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="modalMode !== 'add'" :gutter="16">
          <a-col :span="8">
            <a-form-item label="合作状态" name="partnershipStatus">
              <a-select
                v-model:value="modalInfo.partnershipStatus"
                :disabled="isViewMode"
                allowClear
              >
                <a-select-option value="1">正式</a-select-option>
                <a-select-option value="2">储备</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 意向产品 -->
        <a-row style="margin-top: 24px">
          <a-col class="import-title">
            意向产品
            <a-button
              v-if="!isViewMode"
              type="primary"
              size="small"
              @click="addProduct"
              style="margin-left: 16px"
            >
              添加产品
            </a-button>
          </a-col>
        </a-row>
        <div class="table-container">
          <a-table
            :dataSource="modalInfo.bdmPreAgentProductRelDTO"
            :columns="productColumns"
            :pagination="false"
            size="small"
            :scroll="{ y: 200 }"
            rowKey="productId"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'agencyExperience'">
                {{ record.agencyExperience === '1' ? '有经验' : '无经验' }}
              </template>
              <template v-else-if="column.dataIndex === 'competitiveExperience'">
                {{ record.competitiveExperience === '1' ? '有经验' : '无经验' }}
              </template>
              <template v-else-if="column.dataIndex === 'competitiveName'">
                {{ record.competitiveExperience === '1' ? record.competitiveName : '-' }}
              </template>
              <template v-else-if="column.dataIndex === 'action'">
                <a-button
                  v-if="!isViewMode"
                  type="link"
                  danger
                  size="small"
                  @click="removeProduct(index)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 意向终端 -->
        <a-row style="margin-top: 24px">
          <a-col class="import-title">
            意向终端
            <a-button
              v-if="!isViewMode"
              type="primary"
              size="small"
              @click="addTerminal"
              style="margin-left: 16px"
            >
              添加终端
            </a-button>
          </a-col>
        </a-row>
        <div class="table-container">
          <a-table
            :dataSource="modalInfo.bdmAgentTerminalRelList"
            :columns="terminalColumns"
            :pagination="false"
            size="small"
            :scroll="{ y: 200 }"
            rowKey="terminalName"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'agencyExperience'">
                {{ record.agencyExperience === '1' ? '有经验' : '无经验' }}
              </template>
              <template v-else-if="column.dataIndex === 'action'">
                <a-button
                  v-if="!isViewMode"
                  type="link"
                  danger
                  size="small"
                  @click="removeTerminal(index)"
                >
                  删除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </a-form>
    </div>
  </a-modal>

  <!-- 产品选择弹窗 -->
  <a-modal
    v-model:visible="showProductModal"
    title="选择意向产品"
    @ok="confirmProduct"
    @cancel="cancelProduct"
    :maskClosable="false"
    destroyOnClose
  >
    <div class="modal_box">
      <a-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="意向产品" name="productId">
          <a-select
            v-model:value="productForm.productId"
            placeholder="请输入产品名称并选择"
            :options="productOptions"
            :field-names="{ label: 'label', value: 'value' }"
            show-search
            :filter-option="false"
            @search="onProductSearch"
            @change="onProductChange"
            allowClear
          />
        </a-form-item>
        <a-form-item label="代理经验" name="agencyExperience">
          <a-radio-group v-model:value="productForm.agencyExperience">
            <a-radio value="1">有经验</a-radio>
            <a-radio value="2">无经验</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="竞品经验" name="competitiveExperience">
          <a-radio-group
            v-model:value="productForm.competitiveExperience"
            @change="onCompetitorExperienceChange"
          >
            <a-radio value="1">有经验</a-radio>
            <a-radio value="2">无经验</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="productForm.competitiveExperience === '1'"
          label="竞品名称"
          name="competitiveName"
        >
          <a-input v-model:value="productForm.competitiveName" placeholder="请输入竞品名称" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>

  <!-- 终端选择弹窗 -->
  <a-modal
    v-model:visible="showTerminalModal"
    title="选择意向终端"
    @ok="confirmTerminal"
    @cancel="cancelTerminal"
    :maskClosable="false"
    destroyOnClose
  >
    <div class="modal_box">
      <a-form
        ref="terminalFormRef"
        :model="terminalForm"
        :rules="terminalRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="终端名称" name="terminalName">
          <a-input v-model:value="terminalForm.terminalName" placeholder="请输入终端名称" />
        </a-form-item>
        <a-form-item label="科室" name="termDepartmentOneName">
          <a-input v-model:value="terminalForm.termDepartmentOneName" placeholder="请输入科室" />
        </a-form-item>
        <a-form-item label="代理经验" name="agencyExperience">
          <a-radio-group v-model:value="terminalForm.agencyExperience">
            <a-radio value="1">有经验</a-radio>
            <a-radio value="2">无经验</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/dailyWork/components/Table/index.vue';
  import { onMounted, reactive, ref, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Modal } from 'ant-design-vue';
  const { notification } = useMessage();
  import {
    getAgentList,
    getAgentDetail,
    getProductSelect,
    getProvince,
    getCityByProvinceCode,
    addAgent,
    editAgent,
    deleteAgent,
    dictionaryDetail,
    getCode,
  } from '/@/api/bdm/chubeiAgent';
  import { getDicDetailList } from '/@/api/system/dic';
  import { debounce } from 'lodash-es';
  import { usePermission } from '/@/hooks/web/usePermission';

  // 基础数据
  const typeOptions: any = ref([]);
  const teamOptions: any = ref([]);
  const fiscalOptions: any = ref([]);
  const provinceOptions: any = ref([]);
  const cityOptions: any = ref([]);
  const primaryCityOptions: any = ref([]);
  const productOptions: any = ref([]);

  const { hasPermission } = usePermission();

  // 表格列定义
  const productColumns = [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
    },
    {
      title: '代理经验',
      dataIndex: 'agencyExperience',
      key: 'agencyExperience',
      width: 100,
    },
    {
      title: '竞品经验',
      dataIndex: 'competitiveExperience',
      key: 'competitiveExperience',
      width: 100,
    },
    {
      title: '竞品名称',
      dataIndex: 'competitiveName',
      key: 'competitiveName',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  const terminalColumns = [
    {
      title: '终端名称',
      dataIndex: 'terminalName',
      key: 'terminalName',
      width: 200,
    },
    {
      title: '科室名称',
      dataIndex: 'termDepartmentOneName',
      key: 'termDepartmentOneName',
      width: 150,
    },
    {
      title: '代理经验',
      dataIndex: 'agencyExperience',
      key: 'agencyExperience',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      fixed: 'right',
    },
  ];

  // 搜索表单
  const searchForm = reactive({
    name: '',
    userId: '',
    type: null,
    partnershipStatus: '2',
  });

  // 模态框相关
  const modalMode = ref('add'); // add, edit, view
  const currentEditId = ref('');

  // 产品和终端弹窗
  const showProductModal = ref(false);
  const showTerminalModal = ref(false);

  // 表单引用
  const productFormRef = ref();
  const terminalFormRef = ref();
  // 计算属性
  const modalTitle = computed(() => {
    switch (modalMode.value) {
      case 'add':
        return '新增储备代理商';
      case 'edit':
        return '编辑储备代理商';
      case 'view':
        return '查看储备代理商详情';
      default:
        return '储备代理商';
    }
  });

  const isViewMode = computed(() => modalMode.value === 'view');

  // 表单数据
  interface ModalInfo {
    id?: string;
    name: string;
    code: string;
    type: string;
    phone: string;
    idNumber: null;
    province: string;
    provinceName: string;
    city: string;
    cityName: string;
    address: string;
    primaryProvince: string;
    primaryProvinceName: string;
    primaryCity: string;
    primaryCityName: string;
    peopleNumber: string;
    peopleNumberName: string;
    fiscalCompetence: string;
    fiscalCompetenceName: string;
    partnershipStatus: string;
    bdmPreAgentProductRelDTO: any[];
    bdmAgentTerminalRelList: any[];
  }

  const modalInfo = ref<ModalInfo>({
    name: '',
    code: '',
    type: '1',
    phone: '',
    idNumber: null,
    province: '',
    provinceName: '',
    city: '',
    cityName: '',
    address: '',
    primaryProvince: '',
    primaryProvinceName: '',
    primaryCity: '',
    primaryCityName: '',
    peopleNumber: '',
    peopleNumberName: '',
    fiscalCompetence: '',
    fiscalCompetenceName: '',
    partnershipStatus: '2',
    bdmPreAgentProductRelDTO: [],
    bdmAgentTerminalRelList: [],
  });

  // 产品表单
  const productForm = ref({
    productId: '',
    productName: '',
    agencyExperience: '1',
    competitiveExperience: '1',
    competitiveName: '',
  });

  // 终端表单
  const terminalForm = ref({
    terminalName: '',
    termDepartmentOneName: '',
    agencyExperience: '1',
  });
  const changeType = () => {
    modalInfo.value.idNumber = null;
  };
  // 表单验证规则
  const formRules = computed(() => {
    const baseRules = {
      name: [{ required: true, message: '请输入代理商名称', trigger: 'blur' }],
      type: [{ required: true, message: '请选择代理商类型', trigger: 'change' }],
      phone: [
        {
          required: true,
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号',
          trigger: 'blur',
        },
      ],
      province: [{ required: true, message: '请选择所在省份', trigger: 'change' }],
      city: [{ required: true, message: '请选择所在城市', trigger: 'change' }],
    };

    // 在编辑模式下，基础信息的所有字段都变成必填
    if (modalMode.value === 'edit') {
      return {
        ...baseRules,
        idNumber: [{ required: true, message: '请输入证件编号', trigger: 'blur' }],
        peopleNumber: [{ required: true, message: '请选择团队人员', trigger: 'change' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        primaryProvince: [{ required: true, message: '请选择主要覆盖省份', trigger: 'change' }],
        primaryCity: [{ required: true, message: '请选择主要覆盖城市', trigger: 'change' }],
        fiscalCompetence: [{ required: true, message: '请选择财税能力', trigger: 'change' }],
        partnershipStatus: [{ required: true, message: '请选择合作状态', trigger: 'change' }],
      };
    }

    return baseRules;
  });

  const productRules = computed(() => ({
    productId: [{ required: true, message: '请选择产品', trigger: 'change' }],
    agencyExperience: [{ required: true, message: '请选择代理经验', trigger: 'change' }],
    competitiveExperience: [{ required: true, message: '请选择竞品经验', trigger: 'change' }],
    competitiveName:
      productForm.value.competitiveExperience === '1'
        ? [{ required: true, message: '请输入竞品名称', trigger: 'blur' }]
        : [],
  }));

  const terminalRules = {
    terminalName: [{ required: true, message: '请输入终端名称', trigger: 'blur' }],
    termDepartmentOneName: [{ required: true, message: '请输入科室', trigger: 'blur' }],
    agencyExperience: [{ required: true, message: '请选择代理经验', trigger: 'change' }],
  };

  // 重置表单数据
  const resetModalInfo = () => {
    modalInfo.value = {
      name: '',
      code: '',
      type: '1',
      phone: '',
      idNumber: null,
      province: '',
      provinceName: '',
      city: '',
      cityName: '',
      address: '',
      primaryProvince: '',
      primaryProvinceName: '',
      primaryCity: '',
      primaryCityName: '',
      peopleNumber: '',
      peopleNumberName: '',
      fiscalCompetence: '',
      fiscalCompetenceName: '',
      partnershipStatus: '2',
      bdmPreAgentProductRelDTO: [],
      bdmAgentTerminalRelList: [],
    };
  };
  const phoneBlur = () => {
    formRef.value.validate('phone');
  };
  // 生成代理商编号
  const generateAgentCode = async () => {
    try {
      const res = await getCode({ encode: 'agentCode' });
      modalInfo.value.code = res;
    } catch (error) {
      console.error('生成代理商编号失败:', error);
    }
  };

  // 重置搜索
  const reSet = () => {
    searchForm.name = '';
    searchForm.userId = '';
    searchForm.type = null;
    getList();
  };

  // 打开新增弹窗
  const handleAdd = () => {
    modalMode.value = 'add';
    currentEditId.value = '';
    resetModalInfo();
    generateAgentCode();
    openSignVisible.value = true;
  };
  const tableColumns = [
    {
      title: '序号',
      isHasIndex: true,
      key: 'No',
      align: 'center',
      width: 50,
    },
    {
      title: '代理商名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '代理商编码',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: '所在省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
      align: 'center',
    },
    {
      title: '主要覆盖省份',
      dataIndex: 'primaryProvinceName',
      key: 'primaryProvinceName',
      align: 'center',
    },
    {
      title: '团队人员数量',
      dataIndex: 'peopleNumber',
      key: 'peopleNumber',
      align: 'center',
    },
    {
      title: '财税能力',
      dataIndex: 'fiscalCompetence',
      key: 'fiscalCompetence',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = reactive<{ data: any[] }>({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });
  const getList = debounce(async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getAgentList(temp);
      pagination.totalItems = res.total ?? 0;
      tableData.data = res.list;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  }, 200);
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  const onDel = (row: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除代理商"${row.name}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteAgent([row.id]);
          if (res) {
            notification.success({
              message: '提示',
              description: '删除成功',
            });
            getList(1);
          }
        } catch (err) {
          console.error('删除失败:', err);
          notification.error({
            message: '提示',
            description: '删除失败',
          });
        }
      },
    });
  };

  // 加载状态
  const cityLoading = ref(false);
  const primaryCityLoading = ref(false);

  const changeCity = async (value: string) => {
    const selectedCity = cityOptions.value.find((item) => item.value === value);
    modalInfo.value.cityName = selectedCity?.label || '';
  };
  const changePrimaryCity = async (value: string) => {
    const selectedCity = cityOptions.value.find((item) => item.value === value);
    modalInfo.value.primaryCityName = selectedCity?.label || '';
  };
  const changePeopleNumber = async (value: string) => {
    const selectedPeopleNumber = teamOptions.value.find((item) => item.value === value);
    modalInfo.value.peopleNumberName = selectedPeopleNumber?.name || '';
  };
  const changeFiscal = async (value: string) => {
    const selectedFiscal = fiscalOptions.value.find((item) => item.value === value);
    modalInfo.value.fiscalCompetenceName = selectedFiscal?.name || '';
  };
  // 省份城市变化处理 - 优化版本
  const onProvinceChange = async (value: string) => {
    modalInfo.value.province = value;
    const selectedProvince = provinceOptions.value.find((item) => item.value === value);
    modalInfo.value.provinceName = selectedProvince?.label || '';
    modalInfo.value.city = '';
    modalInfo.value.cityName = '';

    if (value) {
      await updateCityOptions(value);
    } else {
      cityOptions.value = [];
    }
  };

  const onPrimaryProvinceChange = async (value: string) => {
    modalInfo.value.primaryProvince = value;
    const selectedProvince = provinceOptions.value.find((item) => item.value === value);
    modalInfo.value.primaryProvinceName = selectedProvince?.label || '';
    modalInfo.value.primaryCity = '';
    modalInfo.value.primaryCityName = '';

    if (value) {
      await updatePrimaryCityOptions(value);
    } else {
      primaryCityOptions.value = [];
    }
  };

  // 更新城市选项 - 优化版本
  const updateCityOptions = async (provinceCode: string) => {
    if (!provinceCode) {
      cityOptions.value = [];
      return;
    }

    cityLoading.value = true;
    try {
      const res = await getCityByProvinceCode({ provinceCode });
      cityOptions.value = res.map((item: any) => ({
        label: item.label,
        value: item.value,
      }));
    } catch (error) {
      console.error('加载城市数据失败:', error);
      cityOptions.value = [];
      notification.error({
        message: '错误',
        description: '加载城市数据失败，请重试',
      });
    } finally {
      cityLoading.value = false;
    }
  };

  const updatePrimaryCityOptions = async (provinceCode: string) => {
    if (!provinceCode) {
      primaryCityOptions.value = [];
      return;
    }

    primaryCityLoading.value = true;
    try {
      const res = await getCityByProvinceCode({ provinceCode });
      primaryCityOptions.value = res.map((item: any) => ({
        label: item.label,
        value: item.value,
      }));
    } catch (error) {
      console.error('加载覆盖城市数据失败:', error);
      primaryCityOptions.value = [];
      notification.error({
        message: '错误',
        description: '加载覆盖城市数据失败，请重试',
      });
    } finally {
      primaryCityLoading.value = false;
    }
  };
  const onProductChange = (value: string) => {
    if (!value) {
      loadProductData();
    }
  };
  // 产品搜索
  const onProductSearch = debounce(async (keyword: string) => {
    try {
      const res = await getProductSelect({ name: keyword });
      productOptions.value = res;
    } catch (error) {
      console.error('搜索产品失败:', error);
    }
  }, 300);

  // 竞品经验变化处理
  const onCompetitorExperienceChange = (e: any) => {
    if (e.target.value === '2') {
      // 选择无经验时清空竞品名称
      productForm.value.competitiveName = '';
    }
  };

  // 产品相关操作
  const addProduct = () => {
    productForm.value = {
      productId: '',
      productName: '',
      agencyExperience: '1',
      competitiveExperience: '1',
      competitiveName: '',
    };
    loadProductData();
    showProductModal.value = true;
  };

  const confirmProduct = async () => {
    try {
      productFormRef.value.validate().then(() => {
        // 检查是否已存在该产品
        const exists = modalInfo.value.bdmPreAgentProductRelDTO.some(
          (item: any) => item.productId === productForm.value.productId,
        );

        if (exists) {
          notification.warning({
            message: '提示',
            description: '该产品已添加，请选择其他产品',
          });
          return;
        }

        // 获取产品名称
        const selectedProduct = productOptions.value.find(
          (item: any) => item.value === productForm.value.productId,
        );

        if (!selectedProduct) {
          notification.error({
            message: '错误',
            description: '未找到选中的产品信息',
          });
          return;
        }

        // 添加产品到列表
        modalInfo.value.bdmPreAgentProductRelDTO.push({
          ...productForm.value,
          productName: selectedProduct.label,
        });

        notification.success({
          message: '成功',
          description: '产品添加成功',
        });

        showProductModal.value = false;
      });
    } catch (error) {
      console.error('添加产品失败:', error);
      notification.error({
        message: '错误',
        description: '添加产品失败，请重试',
      });
    }
  };

  const cancelProduct = () => {
    showProductModal.value = false;
  };

  const removeProduct = (index: number) => {
    modalInfo.value.bdmPreAgentProductRelDTO.splice(index, 1);
  };

  // 终端相关操作
  const addTerminal = () => {
    terminalForm.value = {
      terminalName: '',
      termDepartmentOneName: '',
      agencyExperience: '1',
    };
    showTerminalModal.value = true;
  };

  const confirmTerminal = async () => {
    try {
      terminalFormRef.value.validate().then(() => {
        // 检查是否已存在该终端（同名终端和科室）
        const exists = modalInfo.value.bdmAgentTerminalRelList.some(
          (item: any) =>
            item.terminalName === terminalForm.value.terminalName &&
            item.termDepartmentOneName === terminalForm.value.termDepartmentOneName,
        );

        if (exists) {
          notification.warning({
            message: '提示',
            description: '该终端和科室已添加',
          });
          return;
        }

        // 添加终端到列表
        modalInfo.value.bdmAgentTerminalRelList.push({
          ...terminalForm.value,
        });

        notification.success({
          message: '成功',
          description: '终端添加成功',
        });

        showTerminalModal.value = false;
      });
    } catch (error) {
      console.error('添加终端失败:', error);
      notification.error({
        message: '错误',
        description: '添加终端失败，请重试',
      });
    }
  };

  const cancelTerminal = () => {
    showTerminalModal.value = false;
  };

  const removeTerminal = (index: number) => {
    modalInfo.value.bdmAgentTerminalRelList.splice(index, 1);
  };

  // 加载产品数据
  const loadProductData = async () => {
    try {
      // const res = await getProductSelect({ name: '' });
      productOptions.value = [];
    } catch (error) {
      console.error('加载产品数据失败:', error);
    }
  };

  // 编辑操作
  const onEdit = (row: any) => {
    modalMode.value = 'edit';
    currentEditId.value = row.id;
    loadAgentDetail(row.id);
  };

  // 查看操作
  const onView = (row: any) => {
    modalMode.value = 'view';
    currentEditId.value = row.id;
    loadAgentDetail(row.id);
  };

  // 加载代理商详情
  const loadAgentDetail = async (id: string) => {
    try {
      const res = await getAgentDetail({ id });
      modalInfo.value = {
        ...res,
        bdmPreAgentProductRelDTO: (res.bdmPreAgentProductRelDTO || []).map((item: any) => ({
          ...item,
          id: null,
        })),
        bdmAgentTerminalRelList: (res.bdmAgentTerminalRelList || []).map((item: any) => ({
          ...item,
          id: null,
        })),
        partnershipStatus: '2',
      };

      // 更新城市数据
      if (modalInfo.value.province) {
        await updateCityOptions(modalInfo.value.province);
      }
      if (modalInfo.value.primaryProvince) {
        await updatePrimaryCityOptions(modalInfo.value.primaryProvince);
      }

      openSignVisible.value = true;
    } catch (error) {
      console.error('加载代理商详情失败:', error);
    }
  };

  // 模态框确认
  const handleModalOk = async () => {
    if (isViewMode.value) return;

    try {
      formRef.value.validate().then(async () => {
        modalLoading.value = true;

        const submitData = { ...modalInfo.value };
        if (modalMode.value === 'add') {
          await addAgent(submitData);
        } else {
          await editAgent({ ...submitData, id: currentEditId.value });
        }
        notification.success({
          message: '提示',
          description: '操作成功',
        });
        openSignVisible.value = false;
        modalLoading.value = false;
        getList(1);
      });
    } catch (error) {
      modalLoading.value = false;
      console.error('保存失败:', error);
    } finally {
      modalLoading.value = false;
    }
  };

  // 模态框取消
  const handleModalCancel = () => {
    openSignVisible.value = false;
  };

  // 初始化数据
  const initData = async () => {
    try {
      // 加载代理商类型
      const typeRes = await getDicDetailList({ itemId: '1866686246655471618' });
      typeOptions.value = typeRes;

      // 加载团队人员
      const teamRes = await dictionaryDetail({ itemId: '1866692077019049985' });
      teamOptions.value = teamRes;

      // 加载财税能力
      const fiscalRes = await dictionaryDetail({ itemId: '1866711273920638978' });
      fiscalOptions.value = fiscalRes;

      // 加载省份数据
      const provinceRes = await getProvince();
      provinceOptions.value = provinceRes.map((item: any) => ({
        label: item.label,
        value: item.value,
      }));
    } catch (error) {
      console.error('初始化数据失败:', error);
    }
  };

  onMounted(async () => {
    getList();
    initData();
  });

  const openSignVisible = ref(false);
  const modalLoading = ref<boolean>(false);
  const formRef = ref();
</script>

<style scoped lang="less">
  #agentRepres {
    width: 100%;
    height: 100%;
    padding: 8px;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .tabs {
        padding: 0 16px;
      }
      .p_box {
        padding: 0 16px;
      }
      .type_top {
        width: 100%;
        padding: 16px 0 0 16px;
        .btn_group {
          display: inline-flex;
          > div {
            padding: 6px 40px;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            cursor: pointer;
            &:first-child {
              border-right: none;
              border-radius: 3px 0 0 3px;
            }
            &:last-child {
              border-left: none;
              border-radius: 0 3px 3px 0;
            }
          }
          .is_active {
            color: #fff;
          }
        }
      }
    }
    .filterForm_box {
      * + * {
        margin-left: 16px;
      }
      .right_btn {
        float: right;
      }
    }
    .table_box {
      padding-bottom: 0 !important;
      .tipRed {
        color: #c10000;
      }
    }
  }
  .modal_box {
    padding: 16px;
  }

  .import-title {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 16px;
    padding-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }

  .table-container {
    margin-top: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;

    :deep(.ant-table) {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
      }

      .ant-table-tbody > tr > td {
        padding: 8px 16px;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #f5f5f5;
      }
    }

    :deep(.ant-empty) {
      margin: 20px 0;
    }
  }

  // 保留原有样式以防需要回退
  .product-list,
  .terminal-list {
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    display: none; // 隐藏原有样式
  }

  .product-item,
  .terminal-item {
    padding: 12px 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #fafbfc;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .product-header,
  .terminal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .product-name,
    .terminal-name {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .product-info,
  .terminal-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;

    &:last-child {
      margin-bottom: 0;
    }
  }
</style>
<style lang="less">
  .full-modal-chubeiAgent {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
      max-height: 100vh !important;
      overflow-y: auto;
    }
  }
</style>
