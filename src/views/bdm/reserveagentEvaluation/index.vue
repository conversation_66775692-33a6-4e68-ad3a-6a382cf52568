<template>
  <ResizePageWrapper :hasLeft="false">
    <template #resizeRight>
      <BasicTable
        @register="registerTable"
        isMenuTable
        ref="tableRef"
        :row-selection="{ selectedRowKeys: selectedKeys, onChange: onSelectChange }"
      >
        <template #toolbar>
          <template v-for="button in tableButtonConfig" :key="button.code">
            <a-button
              v-if="button.isDefault"
              type="primary"
              v-auth="`reagentevaluation:${button.code}`"
              @click="buttonClick(button.code)"
            >
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
            <a-button v-else type="primary">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="getActions(record)" />
          </template>

          <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
          </template>
          <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
              record[column.dataIndex]
            }}</span>
          </template>
        </template>
      </BasicTable>
    </template>

    <AgentEvaluationModal @register="registerModal" @success="handleSuccess" />
    <ImportModal
      @register="registerImportModal"
      importUrl="/bdm/agentevaluation/import"
      @success="handleImportSuccess"
    />
    <ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="columns"
      @success="handleExportSuccess"
    />
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import { ref, computed, createVNode } from 'vue';

  import { Modal } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
  import {
    getBdmAgentEvaluationPage,
    deleteBdmAgentEvaluation,
    getBdmAgentEvaluation,
    exportBdmAgentEvaluation,
  } from '/@/api/bdm/agentevaluation';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { executeListStyle } from '/@/hooks/web/useListStyle'; //列表样式配置
  import { useRouter } from 'vue-router';

  import { useModal } from '/@/components/Modal';

  import AgentEvaluationModal from './components/reserveAgentEvaluationModal.vue';

  import { downloadByData } from '/@/utils/file/download';
  import ExportModal from '/@/views/form/template/components/ExportModal.vue';

  import { searchFormSchema, columns } from './components/config';

  import Icon from '/@/components/Icon/index';
  import { ImportModal } from '/@/components/Import';
  import { useUserStore } from '/@/store/modules/user';
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  const filterColumns = filterColumnAuth(columns);
  const tableRef = ref();
  const pageParamsInfo = ref<any>({});

  const visibleExport = ref(false);

  //展示在列表内的按钮
  const actionButtons = ref<string[]>([
    'view',
    'edit',
    'copyData',
    'delete',
    'startwork',
    'flowRecord',
    'pushorder',
  ]);
  const buttonConfigs = computed(() => {
    const list = [
      {
        buttonId: '1897167238310588416',
        name: '查看',
        code: 'view',
        icon: 'ant-design:eye-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1897167238310588417',
        name: '新增',
        code: 'add',
        icon: 'ant-design:plus-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1897167238310588418',
        name: '编辑',
        code: 'edit',
        icon: 'ant-design:form-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1897167238310588419',
        name: '批量删除',
        code: 'batchdelete',
        icon: 'ant-design:delete-outlined',
        isDefault: true,
        isUse: true,
      },
      // {
      //   isUse: true,
      //   name: '快速导入',
      //   code: 'import',
      //   icon: 'ant-design:import-outlined',
      //   isDefault: true,
      // },
      {
        buttonId: '1897167238310588420',
        name: '快速导出',
        code: 'export',
        icon: 'ant-design:export-outlined',
        isDefault: true,
        isUse: true,
      },
      {
        buttonId: '1897167238314782720',
        name: '删除',
        code: 'delete',
        icon: 'ant-design:delete-outlined',
        isDefault: true,
        isUse: true,
      },
    ];
    return filterButtonAuth(list);
  });

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const btnEvent = {
    view: handleView,
    add: handleAdd,
    edit: handleEdit,
    batchdelete: handleBatchdelete,
    export: handleExport,
    delete: handleDelete,
    import: handleImport,
  };

  const { currentRoute } = useRouter();

  const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

  const selectedKeys = ref<string[]>([]);
  const selectedRowsData = ref<any[]>([]);

  const [registerModal, { openModal }] = useModal();
  const userStore = useUserStore();

  const [registerTable, { reload }] = useTable({
    title: '储备代理商评估',
    api: getBdmAgentEvaluationPage,
    rowKey: 'id',
    columns: filterColumns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: false,
    },
    beforeFetch: (params) => {
      pageParamsInfo.value = {
        ...params,
        FormId: formIdComputedRef.value,
        partnershipStatus: '2',
        PK: 'id',
        userId: userStore.getUserInfo.id,
      };
      return pageParamsInfo.value;
    },
    afterFetch: (res) => {
      tableRef.value.setToolBarWidth();

      selectedKeys.value = [];
      selectedRowsData.value = [];
    },
    useSearchForm: true,
    showTableSetting: true,

    striped: false,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow,
    isAdvancedQuery: false,
    querySelectOption: JSON.stringify(searchFormSchema),
    objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
  });

  function buttonClick(code) {
    btnEvent[code]();
  }

  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteList([record.id]);
  }

  function handleBatchdelete() {
    if (selectedKeys.value.length == 0) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要删除的数据'),
      });
      return;
    }

    //与工作流相关的数据不能进行批量删除
    const cantDelete = selectedRowsData.value.filter((x) => {
      return (
        (x.workflowData?.enabled && x.workflowData?.status) ||
        (!x.workflowData?.enabled && !!x.workflowData?.processId)
      );
    });
    if (cantDelete.length) {
      notification.warning({
        message: 'Tip',
        description: t('含有不能删除的数据'),
      });
      return;
    }
    deleteList(selectedKeys.value);
  }
  function deleteList(ids) {
    const partnershipStatus = '2';
    Modal.confirm({
      title: '提示信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认删除？',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        deleteBdmAgentEvaluation(ids, partnershipStatus).then((_) => {
          handleSuccess();
          notification.success({
            message: 'Tip',
            description: t('删除成功！'),
          });
        });
      },
      onCancel() {},
    });
  }

  function onSelectChange(selectedRowKeys: [], selectedRows) {
    selectedKeys.value = selectedRowKeys;
    selectedRowsData.value = selectedRows;
  }
  function customRow(record: Recordable) {
    return {
      onClick: () => {
        let selectedRowKeys = [...selectedKeys.value];
        if (selectedRowKeys.indexOf(record.id) >= 0) {
          let index = selectedRowKeys.indexOf(record.id);
          selectedRowKeys.splice(index, 1);
        } else {
          selectedRowKeys.push(record.id);
        }
        selectedKeys.value = selectedRowKeys;
      },
      ondblclick: () => {
        if (record.isCanEdit && hasPermission('reagentevaluation:edit')) {
          handleEdit(record);
        }
      },
    };
  }

  function handleSuccess() {
    selectedKeys.value = [];
    selectedRowsData.value = [];
    reload();
  }

  function handleView(record: Recordable) {
    openModal(true, {
      isView: true,
      id: record.id,
    });
  }

  async function handleExport() {
    visibleExport.value = true;
  }
  async function handleExportSuccess(cols) {
    const res = await exportBdmAgentEvaluation({
      isTemplate: false,
      columns: cols.toString(),
      ...pageParamsInfo.value,
    });
    visibleExport.value = false;
    downloadByData(
      res.data,
      '储备代理商评估.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  function getActions(record: Recordable): ActionItem[] {
    record.isCanEdit = false;

    const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        return {
          icon: button?.icon,
          auth: `reagentevaluation:${button.code}`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        if (button.code === 'view') {
          return {
            icon: button?.icon,
            auth: `reagentevaluation:${button.code}`,
            tooltip: button?.name,
            onClick: btnEvent[button.code].bind(null, record),
          };
        } else {
          return {};
        }
      }
    });
    return actionsList;
  }
  function handleImport() {
    openImportModal(true, {
      title: '快速导入',
      //templateTitle: '保证金操作记录',
      //downLoadUrl: '/bdm/deposit/export',
    });
  }
  function handleImportSuccess() {
    reload();
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }
  .show {
    display: flex;
  }
  .hide {
    display: none !important;
  }
</style>
