export const permissionList = [
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '终端名称',
    fieldId: 'terminalId',
    isSubTable: false,
    showChildren: true,
    type: 'associate-popup',
    key: '130f60a1d03148278fa3a3478c1559c5',
    children: [],
    options: {},
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商名称',
    fieldId: 'agentId',
    isSubTable: false,
    showChildren: true,
    type: 'associate-popup',
    key: '6f99c18ea24940e888df07559589ace7',
    children: [],
    options: {},
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商联系人',
    fieldId: 'contactPerson',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'c3ab9f1f1de64c1099832c6ee325d018',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '联系电话',
    fieldId: 'phone',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'c6450c121a314de9858df97365c28122',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '身份证号',
    fieldId: 'personIdNumber',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '8262366c702f4f5b952e005457b6931d',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '详细地址',
    fieldId: 'address',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '7f42a9fdb59f4036bc00e8142e1417da',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '评估模板',
    fieldId: 'templeteId',
    isSubTable: false,
    showChildren: true,
    type: 'associate-popup',
    key: '3441cdfb9a344a8f828bbc9f1c2cd56c',
    children: [],
    options: {},
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '得分',
    fieldId: 'totalScore',
    isSubTable: false,
    showChildren: true,
    type: 'number',
    key: '24e9055c9164476f8eb5fa73d3054e83',
    children: [],
    options: {},
    defaultValue: 0,
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '状态',
    fieldId: 'enabledMark',
    isSubTable: false,
    showChildren: true,
    type: 'switch',
    key: 'b075d328d03044b39cac9a0c557dd9f8',
    children: [],
    options: {},
    defaultValue: 1,
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '备注',
    fieldId: 'remark',
    isSubTable: false,
    showChildren: true,
    type: 'textarea',
    key: '868117b6de8c48c583f4163cf77bc55c',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: 'id',
    fieldId: 'id',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'e87eb38b93bf4891bd2f14b6e2289051',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '年度',
    fieldId: 'year',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '65962088bc9743dfb7dde71bd8719c0c',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '终端名称（隐藏）',
    fieldId: 'terminalName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: 'a29d03a749554a5cbada1c893a2b8c90',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '代理商名称（隐藏）',
    fieldId: 'agentName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '8f4c4fac89f54b699be94186cb7429dd',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: false,
    view: true,
    edit: true,
    disabled: false,
    isSaveTable: false,
    tableName: '',
    fieldName: '评估模板名称（隐藏）',
    fieldId: 'templeteName',
    isSubTable: false,
    showChildren: true,
    type: 'input',
    key: '639d9f947654457fa708c5edd161385d',
    children: [],
    options: {},
    defaultValue: '',
  },
  {
    required: true,
    view: true,
    edit: true,
    disabled: false,
    isSubTable: true,
    showChildren: false,
    tableName: 'bdmAgentEvaluationRecordList',
    fieldName: '评估指标',
    fieldId: 'bdmAgentEvaluationRecordList',
    type: 'form',
    key: 'b248dda6e67e4374ab98f438861f7daa',
    children: [
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: 'id',
        fieldId: 'id',
        key: '933482a6b1934f98a648e1c5a7560baf',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: 'opbject_id',
        fieldId: 'objectId',
        key: '1d6ca6c151124672a8f873a8df0bb873',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '维度编号',
        fieldId: 'typeCode',
        key: '43be556655f44784ac10a91e9aeedf88',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '维度名称',
        fieldId: 'typeName',
        key: 'b69386e93fd243a0a1c8ebf5ae753979',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '指标id',
        fieldId: 'indicatorId',
        key: '08aaabf8aebe4df2b93b09e4161bbde8',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '指标名称',
        fieldId: 'indicatorName',
        key: 'a10e4a377bd24753a3504982dc6f9934',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '选项id',
        fieldId: 'optionId',
        key: '86f73a61bd6b4c5cb0d63213af79cc61',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '选项名称',
        fieldId: 'optionName',
        key: 'c6ccd3be8cfa4b42a1915c9b40a14f7e',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '维度排序',
        fieldId: 'typeSort',
        key: '7a05d3e57fa646d8a21bc49ba65a10a8',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '指标排序',
        fieldId: 'indicatorSort',
        key: '6354168bc352468b9adbc471bd11dd68',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '得分',
        fieldId: 'score',
        key: 'c1218d0d034d412d973671aa65241237',
        children: [],
      },
      {
        required: true,
        view: true,
        edit: true,
        disabled: false,
        isSubTable: true,
        isSaveTable: false,
        showChildren: false,
        tableName: 'bdmAgentEvaluationRecordList',
        fieldName: '备注',
        fieldId: 'remark',
        key: 'c76febc8d06e4a26882b3b0d3bde1a08',
        children: [],
      },
    ],
  },
];
