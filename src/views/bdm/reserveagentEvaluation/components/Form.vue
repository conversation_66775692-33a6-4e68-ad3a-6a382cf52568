<template>
  <div>
    <SimpleForm
      ref="systemFormRef"
      :formProps="data.formDataProps"
      :formModel="state.formModel"
      :isWorkFlow="props.fromPage != FromPageType.MENU"
    >
      <template #terminalName="{ formModel, field }">
        <a-input-search
          v-model:value="formModel[field]"
          placeholder="请选择终端名称"
          maxLength="50"
          @search="onSearch"
          :readonly="true"
          :disabled="isView"
        >
          <template #enterButton>
            <a-button class="check_btn" :disabled="isView"> 选择 </a-button>
          </template>
        </a-input-search>
      </template>

      <!--
<template #buttonBefore>
    </template> -->
    </SimpleForm>
    <div class="eval-container">
      <div v-for="(group, typeName) in groupedIndicators" :key="typeName">
        <div class="type-card">
          <div class="font-style"
            >{{ typeName }}
            <span v-if="isView" style="margin-left: 20px; font-size: 15px"
              >维度总分：{{ group[0].typeScore }}</span
            >
          </div>
          <!-- {{ group }} -->
          <div v-for="item in group" :key="item.indicatorId" class="indicator-item">
            <!-- <div style="display: flex; justify-items: flex-start;">
              <div>
                <h4>{{ item.name }}</h4>
                <a-select :disabled="isView" v-model:value="item.optionId" @change="(val) => handleChange(item, val)"
                  placeholder="请选择" style="width: 300px">
                  <a-select-option v-for="option in item.bdmAgentEvalIndicatorOptionList" :key="option.id"
                    :value="option.id">
                    {{ option.name }}
                  </a-select-option>
                </a-select>
              </div>
              <div style="margin-left: 10px;">
                <h4>具体说明/数值</h4>
                <a-input :disabled="isView" style="width: 300px" placeholder="请输入具体说明/数值" v-model:value="item.remark"></a-input>
              </div>
            </div> -->
            <a-row>
              <a-col :span="16">
                <!-- <div style="display: flex; justify-content: space-between;"> -->
                <a-row :gutter="30">
                  <a-col :span="12">
                    <div>
                      <h4 class="title">{{ item.name }}</h4>
                      <a-select
                        :disabled="isView"
                        v-model:value="item.optionId"
                        style="width: 100%"
                        @change="(val) => handleChange(item, val)"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="option in item.bdmAgentEvalIndicatorOptionList"
                          :key="option.id"
                          :value="option.id"
                        >
                          {{ option.name }}
                        </a-select-option>
                      </a-select>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div>
                      <h4 class="title">具体说明/数值</h4>
                      <a-input
                        :disabled="isView"
                        style="width: 100%"
                        placeholder="请输入具体说明/数值"
                        v-model:value="item.remark"
                      />
                    </div>
                  </a-col>
                </a-row>
                <!-- </div> -->
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>
    <!-- <pre style="margin-top: 20px">{{ arr }}</pre> -->
  </div>
  <RadioModal
    :columns="columns"
    :showSearchForm="false"
    @register="registerModal"
    :api="getreserveTerminalPage"
    @success="handleSuccess"
  />
</template>
<script lang="ts" setup>
  import { reactive, ref, onMounted, nextTick, computed, watch, createVNode } from 'vue';
  import { formProps, formEventConfigs, outTemplateId } from './config';
  import SimpleForm from '/@/components/SimpleForm/src/SimpleForm.vue';
  import {
    addBdmAgentEvaluation,
    getBdmAgentEvaluation,
    updateBdmAgentEvaluation,
    getAgentValuationByAgentId,
  } from '/@/api/bdm/agentevaluation';
  import RadioModal from '/@/components/RadioModal/RadioModal.vue';
  import { getreserveTerminalPage } from '/@/api/crm/terminal';
  import { cloneDeep, isString } from 'lodash-es';
  import { FormDataProps } from '/@/components/Designer/src/types';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { FromPageType } from '/@/enums/workflowEnum';
  import {
    createFormEvent,
    getFormDataEvent,
    loadFormEvent,
    submitFormEvent,
  } from '/@/hooks/web/useFormEvent';
  import { changeWorkFlowForm, changeSchemaDisabled } from '/@/hooks/web/useWorkFlowForm';
  import { WorkFlowFormParams } from '/@/model/workflow/bpmnConfig';
  import { useRouter } from 'vue-router';
  import { getBdmAgentEvalTemplate } from '/@/api/base/agentevaltemplete';
  import { Modal } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { BasicColumn } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useUserStore } from '/@/store/modules/user';
  const { filterFormSchemaAuth } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const RowKey = 'id';
  const emits = defineEmits(['changeUploadComponentIds', 'loadingCompleted']);
  const props = defineProps({
    fromPage: {
      type: Number,
      default: FromPageType.MENU,
    },
    isView: {
      type: Boolean,
      default: false,
    },
  });
  const systemFormRef = ref();
  const data: { formDataProps: FormDataProps } = reactive({
    formDataProps: cloneDeep(formProps),
  });
  const state = reactive({
    formModel: {},
    formInfo: { formId: '', formName: '' },
  });
  const { currentRoute } = useRouter();

  // 临时指标数据
  const arr = ref([]);

  watch(
    () => outTemplateId.value,
    async (val) => {
      if (val) {
        let obj = await getBdmAgentEvalTemplate(val);
        arr.value = obj.bdmAgentEvalTmplIndRelList;
        // console.log(arr.value)
      }
    },
  );
  watch(
    () => props.isView,
    async (val) => {
      console.log(val);
      if (val) {
        // console.log("chengogn")
        // let obj = await getBdmAgentEvalTemplate(val);
        // arr.value = obj.bdmAgentEvalTmplIndRelList;
        // // console.log(arr.value)
      }
    },
  );

  // 分组计算（使用本地 arr）
  const groupedIndicators = computed(() => {
    return arr.value.reduce((acc, cur) => {
      const key = cur.typeName;
      if (!acc[key]) acc[key] = [];
      acc[key].push(cur);
      return acc;
    }, {});
  });

  // 处理选项变化
  const handleChange = (item, selectedOptionId) => {
    const option = item.bdmAgentEvalIndicatorOptionList.find((o) => o.id === selectedOptionId);
    if (option) {
      item.optionId = selectedOptionId;
      item.optionName = option.name;
      item.score = option.point;
    } else {
      item.optionId = null;
      item.optionName = '';
      item.score = '';
    }
  };

  onMounted(async () => {
    outTemplateId.value = '';
    try {
      if (props.fromPage == FromPageType.MENU) {
        // setMenuPermission();
        if (currentRoute.value.meta) {
          state.formInfo.formName =
            currentRoute.value.meta.title && isString(currentRoute.value.meta.title)
              ? currentRoute.value.meta.title
              : '';
          state.formInfo.formId =
            currentRoute.value.meta.formId && isString(currentRoute.value.meta.formId)
              ? currentRoute.value.meta.formId
              : '';
        }
        await createFormEvent(
          formEventConfigs,
          state.formModel,
          systemFormRef.value,
          formProps.schemas,
          true,
          state.formInfo.formName,
          state.formInfo.formId,
        ); //表单事件：初始化表单
        await nextTick();
        await loadFormEvent(
          formEventConfigs,
          state.formModel,
          systemFormRef.value,
          formProps.schemas,
          true,
          state.formInfo.formName,
          state.formInfo.formId,
        ); //表单事件：加载表单
      } else if (props.fromPage == FromPageType.FLOW) {
        emits('loadingCompleted'); //告诉系统表单已经加载完毕
        // loadingCompleted后 工作流页面直接利用Ref调用setWorkFlowForm方法
      } else if (props.fromPage == FromPageType.PREVIEW) {
        // 预览 无需权限，表单事件也无需执行
      } else if (props.fromPage == FromPageType.DESKTOP) {
        // 桌面设计 表单事件需要执行
        emits('loadingCompleted'); //告诉系统表单已经加载完毕
        await createFormEvent(
          formEventConfigs,
          state.formModel,
          systemFormRef.value,
          formProps.schemas,
          true,
          state.formInfo.formName,
          state.formInfo.formId,
        ); //表单事件：初始化表单
        await loadFormEvent(
          formEventConfigs,
          state.formModel,
          systemFormRef.value,
          formProps.schemas,
          true,
          state.formInfo.formName,
          state.formInfo.formId,
        ); //表单事件：加载表单
      }
    } catch (error) {}
  });
  // 根据菜单页面权限，设置表单属性（必填，禁用，显示）
  function setMenuPermission() {
    data.formDataProps.schemas = filterFormSchemaAuth(data.formDataProps.schemas!);
  }

  // 校验form 通过返回表单数据
  async function validate() {
    let values = [];
    try {
      values = await systemFormRef.value?.validate();
      //添加隐藏组件
      if (data.formDataProps.hiddenComponent?.length) {
        data.formDataProps.hiddenComponent.forEach((component) => {
          values[component.bindField] = component.value;
        });
      }
    } finally {
    }
    return values;
  }
  // 根据行唯一ID查询行数据，并设置表单数据   【编辑】
  async function setFormDataFromId(rowId) {
    try {
      const record = await getBdmAgentEvaluation(rowId);
      arr.value = JSON.parse(record.content).bdmAgentEvaluationRecordList;
      // console.log(arr.value)
      setFieldsValue(record);
      state.formModel = record;
      await getFormDataEvent(
        formEventConfigs,
        state.formModel,
        systemFormRef.value,
        formProps.schemas,
        true,
        state.formInfo.formName,
        state.formInfo.formId,
      ); //表单事件：获取表单数据
    } catch (error) {}
  }
  // 辅助设置表单数据
  function setFieldsValue(record) {
    systemFormRef.value.setFieldsValue(record);
  }
  // 重置表单数据
  async function resetFields() {
    await systemFormRef.value.resetFields();
  }
  //  设置表单数据全部为Disabled  【查看】
  async function setDisabledForm() {
    data.formDataProps.schemas = changeSchemaDisabled(cloneDeep(data.formDataProps.schemas));
  }
  // 获取行键值
  function getRowKey() {
    return RowKey;
  }
  async function updateSchema(value) {
    await systemFormRef.value.updateSchema(value);
  }
  // 更新api表单数据
  async function update({ values, rowId }) {
    try {
      values[RowKey] = rowId;
      values.bdmAgentEvaluationRecordList = arr.value;
      state.formModel = values;
      values.partnershipStatus = '2';
      let saveVal = await addBdmAgentEvaluation(values);
      await submitFormEvent(
        formEventConfigs,
        state.formModel,
        systemFormRef.value,
        formProps.schemas,
        true,
        state.formInfo.formName,
        state.formInfo.formId,
      ); //表单事件：提交表单
      return saveVal;
    } catch (error) {}
  }

  async function add(values) {
    try {
      // 提取公共的保存逻辑
      const saveLogic = async () => {
        values.bdmAgentEvaluationRecordList = arr.value;
        state.formModel = values;
        values.partnershipStatus = '2';
        console.log(values, 'value值');
        let saveVal = await addBdmAgentEvaluation(values);
        await submitFormEvent(
          formEventConfigs,
          state.formModel,
          systemFormRef.value,
          formProps.schemas,
          true,
          state.formInfo.formName,
          state.formInfo.formId,
        ); // 表单事件：提交表单
        return saveVal;
      };

      // 调用获取代理商估值的接口
      let obj = await getAgentValuationByAgentId({
        agentId: values.agentId,
        terminalId: values.terminalId,
        year: values.year,
      });

      let result; // 用于存储结果

      if (!obj) {
        // 如果不存在评估，弹出 Modal 确认是否覆盖
        const confirmResult = await new Promise((resolve) => {
          Modal.confirm({
            title: '提示信息',
            icon: createVNode(ExclamationCircleOutlined),
            content: '该代理商与终端已存在启用评估，是否要覆盖？',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
              result = await saveLogic();
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        });

        if (!confirmResult) {
          return undefined;
        }
      } else {
        result = await saveLogic();
      }

      return result; // 返回最终结果
    } catch (error) {
      console.error('发生错误：', error);
      // 根据需要处理错误情况
    }
  } // 根据工作流页面权限，设置表单属性（必填，禁用，显示）
  async function setWorkFlowForm(obj: WorkFlowFormParams) {
    try {
      state.formInfo.formId = obj.formId;
      state.formInfo.formName = obj.formName;
      let flowData = changeWorkFlowForm(cloneDeep(formProps), obj);
      let { buildOptionJson, uploadComponentIds, formModels, isViewProcess } = flowData;
      data.formDataProps = buildOptionJson;
      emits('changeUploadComponentIds', uploadComponentIds); //工作流中必须保存上传组件id【附件汇总需要】
      if (isViewProcess) {
        setDisabledForm(); //查看
      }
      state.formModel = formModels;
      setFieldsValue(formModels);
    } catch (error) {}
    await createFormEvent(
      formEventConfigs,
      state.formModel,
      systemFormRef.value,
      formProps.schemas,
      true,
      state.formInfo.formName,
      state.formInfo.formId,
    ); //表单事件：初始化表单
    await loadFormEvent(
      formEventConfigs,
      state.formModel,
      systemFormRef.value,
      formProps.schemas,
      true,
      state.formInfo.formName,
      state.formInfo.formId,
    ); //表单事件：加载表单
  }

  function onSearch(e) {
    openModal(true, {});
  }

  const columns: BasicColumn[] = [
    {
      title: '终端名称',
      dataIndex: 'terminalName',
      width: 100,
    },
  ];
  // const userStore = useUserStore();

  function handleSuccess(e, option) {
    console.log(e);
    console.log(option, '选中');
    // endDate.value = e.endDate;
    // agentId.value = e.id;
    setFieldsValue({
      terminalId: e[0],
      terminalName: option[0].terminalName,
      // userId: userStore.getUserInfo.id,
    });
  }

  function setArr(e) {
    arr.value = e;
  }

  defineExpose({
    setFieldsValue,
    resetFields,
    validate,
    add,
    update,
    setFormDataFromId,
    setDisabledForm,
    setMenuPermission,
    setWorkFlowForm,
    getRowKey,
    setArr,
    updateSchema,
  });
</script>

<style scoped>
  ::v-deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
    color: #000;
  }

  .ant-input[disabled] {
    color: #000;
  }

  .font-style {
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
  }

  .title {
    margin-top: 10px;
  }
</style>
