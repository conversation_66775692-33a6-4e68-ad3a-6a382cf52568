import { FormProps, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { getAgentEvalTemplateSelect } from '/@/api/bdm/agentevaluation';
import { ref } from 'vue';
import { getYears } from '/@/api/bdm/payer';
import { useUserStore } from '/@/store/modules/user';
export const outTemplateId = ref();
export const searchFormSchema: FormSchema[] = [
  {
    field: 'terminalName',
    label: '终端名称',
    component: 'Input',
  },
  {
    field: 'agentName',
    label: '储备代理商名称',
    component: 'Input',
  },
  {
    field: 'year',
    label: '年度',
    type: 'select',
    component: 'ApiSelect',
    defaultValue: [],
    componentProps: () => {
      return {
        width: '100%',
        placeholder: '请选择年度',
        showLabel: true,
        showSearch: true,
        disabled: false,
        api: getYears,
        labelField: 'label',
        valueField: 'value',
        required: true,
        rules: [],
        events: {},
        isShow: true,
        style: { width: '100%' },
      };
    },
  },
];
const userStore = useUserStore();
export const columns: BasicColumn[] = [
  // {
  //   resizable: true,
  //   dataIndex: 'year',
  //   title: '年度',
  //   componentType: 'input',

  //   sorter: true,

  //   styleConfig: undefined,
  //   listStyle: '',
  // },
  {
    resizable: true,
    dataIndex: 'agentName',
    title: '储备代理商名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'terminalName',
    title: '终端名称',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'year',
    title: '年度',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'contactPerson',
    title: '代理商联系人',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'phone',
    title: '联系电话',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },


  {
    resizable: true,
    dataIndex: 'totalScore',
    title: '评估总分',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'typeScore1',
    title: '终端资源',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'typeScore2',
    title: '签约医院过往业绩',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'typeScore3',
    title: '合作意愿',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'typeScore4',
    title: '学术能力',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'typeScore5',
    title: '风险控制',
    componentType: 'number',

    sorter: true,

    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'evalStatus',
    title: '评估状态',
    componentType: 'input',

    sorter: true,

    styleConfig: undefined,
    listStyle: '',
  },
];
//表单事件
export const formEventConfigs = {
  0: [
    {
      type: 'circle',
      color: '#2774ff',
      text: '开始节点',
      icon: '#icon-kaishi',
      bgcColor: '#D8E5FF',
      isUserDefined: false,
    },
    {
      color: '#F6AB01',
      icon: '#icon-chushihua',
      text: '初始化表单',
      bgcColor: '#f9f5ea',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  1: [
    {
      color: '#B36EDB',
      icon: '#icon-shujufenxi',
      text: '获取表单数据',
      detail: '(新增无此操作)',
      bgcColor: '#F8F2FC',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  2: [
    {
      color: '#F8625C',
      icon: '#icon-jiazai',
      text: '加载表单',
      bgcColor: '#FFF1F1',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  3: [
    {
      color: '#6C6AE0',
      icon: '#icon-jsontijiao',
      text: '提交表单',
      bgcColor: '#F5F4FF',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  4: [
    {
      type: 'circle',
      color: '#F8625C',
      text: '结束节点',
      icon: '#icon-jieshuzhiliao',
      bgcColor: '#FFD6D6',
      isLast: true,
      isUserDefined: false,
    },
  ],
};
export const formProps: FormProps = {
  labelCol: { span: 3, offset: 0 },
  labelAlign: 'right',
  layout: 'vertical',
  size: 'default',
  rowProps: { gutter: 30 }, //整体行间距
  schemas: [
    {
      key: 'd6016b264b96493f947341d7e7ee9dgh',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '基本信息',
      componentProps: {
        defaultValue: '基本信息',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: '6f99c18ea24940e888df07559589ace8',
      field: 'agentName',
      label: '储备代理商名称',
      type: 'associate-popup',
      component: 'MultiplePopup',
      colProps: { span: 8 },
      componentProps: {
        popupType: 'associate',
        width: '100%',
        span: '15',
        placeholder: '请选择储备代理商名称',
        showLabel: true,
        disabled: false,
        datasourceType: 'api',
        labelField: 'label',
        valueField: 'value',
        apiConfig: {
          path: '/bdm/agent/getPreAgentSelect',
          method: 'GET',
          apiId: '3cea68b772dd41feaa471396d6dec0b1',
          apiParams: [
            {
              key: '1',
              title: 'Query Params',
              tableInfo: [
                { name: 'name', value: '', required: false, bindType: 'value' },
                {
                  name: 'userId',
                  value: userStore.getUserInfo.id,
                  required: false,
                  bindType: 'value',
                },
              ],
            },
            { key: '2', title: 'Header', tableInfo: [] },
            { key: '3', title: 'Body' },
          ],
          script:
            "var sql = 'select id as value ,name as label,contact_person as contactPerson,person_id_number as personIdNumber,phone,address from bdm_agent where delete_mark=0 and enabled_mark=1 ?{keyword, and name =#{keyword}}';\r\nreturn db.select(sql);",
          outputParams: [
            {
              name: 'label',
              tableTitle: '储备代理商名称',
              bindField: 'agentName',
              show: true,
              width: 150,
              component: '8f4c4fac89f54b699be94186cb7429dd',
            },
            {
              name: 'contact_person',
              tableTitle: '代理商联系人',
              bindField: 'contactPerson',
              show: true,
              width: 150,
              component: 'c3ab9f1f1de64c1099832c6ee325d018',
            },
            {
              name: 'phone',
              tableTitle: '联系电话',
              bindField: 'phone',
              show: true,
              width: 150,
              component: 'c6450c121a314de9858df97365c28122',
            },
            {
              name: 'personIdNumber',
              tableTitle: '身份证号',
              bindField: 'personIdNumber',
              show: false,
              width: 150,
              component: '8262366c702f4f5b952e005457b6931d',
            },
            {
              name: 'address',
              tableTitle: '详细地址',
              bindField: 'address',
              show: false,
              width: 150,
              component: '7f42a9fdb59f4036bc00e8142e1417da',
            },
          ],
        },
        dicOptions: [
          {
            dataIndex: 'name',
            title: 'name',
            name: 'name',
            bindField: 'agentName',
            tableTitle: '储备代理商名称',
            show: true,
            width: 150,
            component: '8f4c4fac89f54b699be94186cb7429dd',
          },
          {
            dataIndex: 'value',
            title: 'value',
            name: 'value',
            tableTitle: '',
            show: false,
            width: 150,
          },
        ],
        required: true,
        rules: [],
        events: {},
        isShow: true,
        itemId: '1858355444867682305',
        style: { width: '100%' },
      },
    },

    {
      key: '130f60a1d03148278fa3a3478c1559c5',
      field: 'terminalId',
      label: '终端名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 0 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: 24,
        defaultValue: '',
        placeholder: '请输入终端名称',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'a29d03a749554a5cbada1c893a2b8c90',
      field: 'terminalName',
      label: '终端名称',
      type: 'input',
      slot: 'terminalName',
      component: 'Slot',
      colProps: { span: 8 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: 24,
        defaultValue: '',
        placeholder: '请输入终端名称',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: true,
        rules: [],
        events: {},

        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '24e9055c9164476f8eb5fa73d3054e83',
      field: 'totalScore',
      label: '评估总分',
      type: 'number',
      component: 'InputNumber',
      colProps: { span: 8 },
      defaultValue: 0,
      componentProps: {
        width: '100%',
        span: 24,
        defaultValue: 0,
        min: 0,
        max: 100,
        step: 1,
        disabled: true,
        showLabel: true,
        controls: true,
        required: false,
        subTotal: false,

        rules: [],
        events: {},
        style: { width: '100%' },
      },
      ifShow: (value) => {
        if (value.model.id) {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      key: 'b075d328d03044b39cac9a0c557dd9f8',
      field: 'enabledMark',
      label: '状态',
      type: 'switch',
      component: 'Switch',
      colProps: { span: 24 },
      defaultValue: 1,
      componentProps: {
        span: '',
        defaultValue: 1,
        checkedChildren: '',
        unCheckedChildren: '',
        checkedColor: '#5e95ff',
        unCheckedColor: '#bbbdbf',
        showLabel: true,
        disabled: false,
        events: {},
        isShow: false,
        style: {},
      },
    },
    // {
    //   key: '868117b6de8c48c583f4163cf77bc55c',
    //   field: 'remark',
    //   label: '备注',
    //   type: 'textarea',
    //   component: 'InputTextArea',
    //   colProps: { span: 24 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '',
    //     defaultValue: '',
    //     placeholder: '请输入备注',
    //     maxlength: 255,
    //     rows: 4,
    //     autoSize: false,
    //     showCount: true,
    //     disabled: false,
    //     showLabel: true,
    //     allowClear: false,
    //     required: false,
    //     isShow: true,
    //     rules: [],
    //     events: {},
    //     style: { width: '100%' },
    //   },
    // },
    {
      key: 'e87eb38b93bf4891bd2f14b6e2289051',
      field: 'id',
      label: 'id',
      type: 'input',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入id',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '65962088bc9743dfb7dde71bd8719c0c',
      field: 'year',
      label: '年度',
      type: 'input',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入年度',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },

    {
      key: '8f4c4fac89f54b699be94186cb7429d1',
      field: 'agentName',
      label: '代理商名称（隐藏）',
      type: 'input',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入代理商名称（隐藏）',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '639d9f947654457fa708c5edd161385d',
      field: 'templateName',
      label: '评估模板名称（隐藏）',
      type: 'input',
      component: 'Input',
      colProps: { span: 24 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入评估模板名称（隐藏）',
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: false,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: 'd6016b264b96493f947341d7e7ee9dth',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '评估指标',
      componentProps: {
        defaultValue: '评估指标',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    // {
    //   key: '6e83b0d728b4460188757f241b60217d',
    //   field: 'bdmAgentEvaluationRecordList',
    //   label: '',
    //   slot: 'bdmAgentEvaluationRecordList',
    //   component: 'Slot',
    //   colProps: { span: 24 },
    //   defaultValue: '',
    //   componentProps: {
    //     width: '100%',
    //     span: '24',
    //     defaultValue: '',
    //     placeholder: '',
    //     maxlength: null,
    //     prefix: '',
    //     suffix: '',
    //     addonBefore: '',
    //     addonAfter: '',
    //     disabled: true,
    //     allowClear: false,
    //     showLabel: true,
    //     required: false,
    //     rules: [],
    //     events: {},
    //     listStyle: '',
    //     isSave: false,
    //     isShow: true,
    //     scan: false,
    //     style: { width: '100%' },
    //   },
    // },
  ],
  showActionButtonGroup: false,
  buttonLocation: 'center',
  actionColOptions: { span: 24 },
  showResetButton: false,
  showSubmitButton: false,
  hiddenComponent: [],
};

//左侧树结构配置
export const treeConfig = {
  id: '',
  isMultiple: false,
  name: '',
  type: 1,
  configTip: '',
  config: [],
};
