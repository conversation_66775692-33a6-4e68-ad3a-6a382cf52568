<template>

  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" @cancel="handleClose"
    :paddingRight="15" :bodyStyle="{ minHeight: '400px !important' }">
    <ModalForm ref="formRef" :fromPage="FromPageType.MENU" :isView="state.isView" />
  </BasicModal>

</template>
<script lang="ts" setup>
import { ref, computed, reactive, provide, Ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { formProps } from './config';
import ModalForm from './Form.vue';
import { FromPageType } from '/@/enums/workflowEnum';
import {
  getInfoWithEnable
} from '/@/api/bdm/agentevaluation';
import { useUserStore } from '/@/store/modules/user';
const emit = defineEmits(['success', 'register']);

const { notification } = useMessage();
const formRef = ref();
const userStore = useUserStore();
const isCopy = ref<boolean>(false)
const state = reactive({
  formModel: {},
  isUpdate: true,
  isView: false,
  rowId: '',
});
provide<Ref<boolean>>('isCopy', isCopy);

const { t } = useI18n();
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {

  state.isUpdate = !!data?.isUpdate;
  state.isView = !!data?.isView;
  isCopy.value = !!data?.isCopy;

  setModalProps({
    destroyOnClose: true,
    maskClosable: false,
    showCancelBtn: !state.isView,
    showOkBtn: !state.isView,
    canFullscreen: true,
    width: 900,
    useWrapper: true, //是否开启自适应高度
    defaultFullscreen: true, //默认全屏
  });
  if (state.isUpdate || state.isView || isCopy.value) {
    state.rowId = data.id;
    if (state.isView) {
      await formRef.value.setDisabledForm();
    }
    if (state.isUpdate) {
      await formRef.value.updateSchema(
        [
          {
            field: 'agentId',
            dynamicDisabled: true,
          },
          {
            field: 'terminalId', dynamicDisabled: true,
          },
        ]
      );
    }
    await formRef.value.setFormDataFromId(state.rowId);
  } else {
    let obj = await getInfoWithEnable({})
    formRef.value.setArr(obj.bdmAgentEvalTmplIndRelList)
    formRef.value.resetFields();
    formRef.value.setFieldsValue({
      userId: userStore.userInfo?.id,
      userName: userStore.userInfo?.name,
      orgId: userStore.userInfo?.departmentId,
      orgName: userStore.userInfo?.departmentName,
      year: obj.year
    });
  }
});

const getTitle = computed(() => (state.isView ? '查看' : state.isUpdate ? '编辑' : isCopy.value ? '复制数据' : '新增'));

async function saveModal() {
  let saveSuccess = false;
  try {
    const values = await formRef.value?.validate();
    //添加隐藏组件
    if (formProps.hiddenComponent?.length) {
      formProps.hiddenComponent.forEach((component) => {
        values[component.bindField] = component.value;
      });
    }
    if (values !== false) {
      try {
        if (!state.isUpdate || isCopy.value) {
          saveSuccess = await formRef.value.add(values);
        } else {
          saveSuccess = await formRef.value.update({ values, rowId: state.rowId });
        }
        return saveSuccess;
      } catch (error) { }
    }
  } catch (error) {
    return saveSuccess;
  }
}

async function handleSubmit() {
  try {
    const saveSuccess = await saveModal();
    setModalProps({ confirmLoading: true });
    if (saveSuccess) {
      if (!state.isUpdate || isCopy.value) {
        //false 新增
        notification.success({
          message: 'Tip',
          description: isCopy.value ? '复制成功' : t('新增成功！'),
        }); //提示消息
      } else {
        notification.success({
          message: 'Tip',
          description: t('修改成功！'),
        }); //提示消息
      }
      closeModal();
      formRef.value.resetFields();
      emit('success');
    }
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

function handleClose() {
  formRef.value.resetFields();
}
</script>
