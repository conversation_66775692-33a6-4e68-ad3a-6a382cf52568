import { FormProps, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const searchFormSchema: FormSchema[] = [
  {
    field: 'terminalName',
    label: '终端名称',
    component: 'Input',
    span: 8,
    componentProps: {
      placeholder: '请输入终端名称',
    },
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入产品名称',
    },
  },
  {
    field: 'productSpecification',
    label: '品规',
    component: 'Input',
    componentProps: {
      placeholder: '请输入品规',
    },
  },
  {
    field: 'customerName',
    label: '销售客户',
    component: 'Input',
    componentProps: {
      placeholder: '请输入销售客户',
    },
  },
  {
    field: 'salesType',
    label: '销售类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择销售类型',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '现款',
          value: 1,
        },
        {
          label: '佣金',
          value: 2,
        },
        {
          label: '底价',
          value: 3,
        },
      ],
    },
  },
  {
    field: 'salesArea',
    label: '销售片区',
    component: 'Input',
    componentProps: {
      placeholder: '请输入销售片区',
    },
  },
  {
    field: 'provincialCompany',
    label: '省公司',
    component: 'Input',
    componentProps: {
      placeholder: '请输入省公司',
    },
  },
  {
    field: 'office',
    label: '办事处',
    component: 'Input',
    componentProps: {
      placeholder: '请输入办事处',
    },
  },
  {
    field: 'yearMonths',
    label: '年月',
    component: 'Slot',
    componentProps: {
      placeholder: '请选择年月',
    },
    slot: 'yearMonths'
  },
  {
    field: 'summaryStatus',
    label: '汇总状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择汇总状态',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '未汇总',
          value: 0,
        },
        {
          label: '已汇总',
          value: 1,
        }
      ],
    },
  },
  {
    field: 'accountingStatus',
    label: '核算状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择核算状态',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '已核算',
          value: 1,
        },
        {
          label: '未核算',
          value: 0,
        }
      ],
    },
  },
];

export const columns: BasicColumn[] = [
  {
    resizable: true,
    dataIndex: 'yearMonths',
    title: '年月',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'flowDetailId',
    title: '商业流向明细id',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'sourceMethod',
    title: '来源方式',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'flowCategory',
    title: '流向分类',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'flowType',
    title: '流向类别',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'businessNature',
    title: '业务性质',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerProvince',
    title: '销售客户省份',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerCode',
    title: '销售客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerName',
    title: '销售客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'commercialLevel',
    title: '商业级别',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'group',
    title: '集团',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'subsidiaryCompany',
    title: '权属公司',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'systems',
    title: '系统',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'salesArea',
    title: '销售片区',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'provincialCompany',
    title: '省公司',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },


  {
    resizable: true,
    dataIndex: 'office',
    title: '办事处',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'validCustomerCode',
    title: '有效客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'validCustomer',
    title: '有效客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchasingCustomerCode',
    title: '购入客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchasingCustomer',
    title: '购入客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'terminalName',
    title: '购入客户协议名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'flowOriginalName',
    title: '流向原始名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'sheng',
    title: '省份',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'productName',
    title: '产品名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'productSpecification',
    title: '品规全称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'salesType',
    title: '销售类型',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '现款', value: 1 },
        { key: 2, label: '佣金', value: 2 },
        { key: 3, label: '底价', value: 3 },
      ];

      return staticOptions.filter((x) => x.value === record.salesType)[0]?.label;
    },
  },
  {
    resizable: true,
    dataIndex: 'purchasingResponsibleJobNumber',
    title: '购入责任人工号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchasingResponsibleName',
    title: '购入责任人姓名',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'batchNumber',
    title: '批号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchaseDateRevised',
    title: '进货日期修订',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'assessmentUnitPrice',
    title: '考核单价',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'quantity',
    title: '数量',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'assessmentAmount',
    title: '考核金额',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'agentName',
    title: '代理商',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'summaryStatus',
    title: '汇总状态',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '已汇总', value: 1 },
        { key: 2, label: '未汇总', value: 0 },
      ];

      return staticOptions.filter((x) => x.value === record.summaryStatus)[0]?.label;
    },
  },
  {
    resizable: true,
    dataIndex: 'accountingStatus',
    title: '核算状态',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '已核算', value: 1 },
        { key: 2, label: '未核算', value: 0 },
      ];

      return staticOptions.filter((x) => x.value === record.accountingStatus)[0]?.label;
    },
  },
];
//表单事件
export const formEventConfigs = {
  0: [
    {
      type: 'circle',
      color: '#2774ff',
      text: '开始节点',
      icon: '#icon-kaishi',
      bgcColor: '#D8E5FF',
      isUserDefined: false,
    },
    {
      color: '#F6AB01',
      icon: '#icon-chushihua',
      text: '初始化表单',
      bgcColor: '#f9f5ea',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  1: [
    {
      color: '#B36EDB',
      icon: '#icon-shujufenxi',
      text: '获取表单数据',
      detail: '(新增无此操作)',
      bgcColor: '#F8F2FC',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  2: [
    {
      color: '#F8625C',
      icon: '#icon-jiazai',
      text: '加载表单',
      bgcColor: '#FFF1F1',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  3: [
    {
      color: '#6C6AE0',
      icon: '#icon-jsontijiao',
      text: '提交表单',
      bgcColor: '#F5F4FF',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  4: [
    {
      type: 'circle',
      color: '#F8625C',
      text: '结束节点',
      icon: '#icon-jieshuzhiliao',
      bgcColor: '#FFD6D6',
      isLast: true,
      isUserDefined: false,
    },
  ],
};
export const formProps: FormProps = {
  labelCol: { span: 20, offset: 0 },
  labelAlign: 'right',
  layout: 'vertical',
  size: 'default',
  rowProps: { gutter: 30 }, //整体行间距
  schemas: [
    {
      field: 'tip',
      label: '',
      component: 'Slot',
      slot: 'tip',
      colProps: { span: 24 },
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        isShow: true,
      }
    },
    {
      field: 'yearMonths',
      label: '选择月份',
      component: 'Slot',
      slot: 'yearMonths',
      colProps: { span: 24 },
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入代理商名称',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '250px' },
      },
    },
    {
      field: 'status',
      label: '汇总状态',
      type: 'input',
      component: 'Select',
      colProps: { span: 24 },
      defaultValue: 0,
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: true,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '250px' },
        options: [
          { label: '已汇总', value: 1 },
          { label: '未汇总', value: 0 },
        ],
      },
    },
  ],
  showActionButtonGroup: false,
  buttonLocation: 'center',
  actionColOptions: { span: 24 },
  showResetButton: false,
  showSubmitButton: false,
  hiddenComponent: [],
};
//左侧树结构配置
export const treeConfig = {
  id: '',
  name: '',
  type: 1,
  config: [],
  configTip: '',
  isMultiple: false,
};
