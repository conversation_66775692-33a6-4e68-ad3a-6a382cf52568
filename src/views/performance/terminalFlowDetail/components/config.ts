import { FormProps, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const searchFormSchema: FormSchema[] = [
  {
    field: 'terminalName',
    label: '终端名称',
    component: 'Input',
    span: 8,
    componentProps: {
      placeholder: '请输入终端名称',
    },
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入产品名称',
    },
  },
  {
    field: 'productSpecification',
    label: '品规',
    component: 'Input',
    componentProps: {
      placeholder: '请输入品规',
    },
  },
  {
    field: 'customerName',
    label: '销售客户',
    component: 'Input',
    componentProps: {
      placeholder: '请输入销售客户',
    },
  },
  {
    field: 'salesType',
    label: '销售类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择销售类型',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '现款',
          value: 1,
        },
        {
          label: '佣金',
          value: 2,
        },
        {
          label: '底价',
          value: 3,
        },
      ],
    },
  },
  {
    field: 'salesArea',
    label: '销售片区',
    component: 'Input',
    componentProps: {
      placeholder: '请输入销售片区',
    },
  },
  {
    field: 'provincialCompany',
    label: '省公司',
    component: 'Input',
    componentProps: {
      placeholder: '请输入省公司',
    },
  },
  {
    field: 'office',
    label: '办事处',
    component: 'Input',
    componentProps: {
      placeholder: '请输入办事处',
    },
  },
  {
    field: 'yearMonths',
    label: '年月',
    component: 'Slot',
    componentProps: {
      placeholder: '请选择年月',
    },
    slot: 'yearMonths'
  },
  {
    field: 'summaryStatus',
    label: '汇总状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择汇总状态',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '未汇总',
          value: 0,
        },
        {
          label: '已汇总',
          value: 1,
        }
      ],
    },
  },
  {
    field: 'accountingStatus',
    label: '核算状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择核算状态',
      getPopupContainer: () => document.body,
      options: [
        {
          label: '已核算',
          value: 1,
        },
        {
          label: '未核算',
          value: 0,
        }
      ],
    },
  },
];

export const columns: BasicColumn[] = [
  {
    resizable: true,
    dataIndex: 'yearMonths',
    title: '年月',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: '',
  },

  {
    resizable: true,
    dataIndex: 'flowDetailId',
    title: '商业流向明细id',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'sourceMethod',
    title: '来源方式',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'flowCategory',
    title: '流向分类',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'flowType',
    title: '流向类别',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'businessNature',
    title: '业务性质',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerProvince',
    title: '销售客户省份',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerCode',
    title: '销售客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'customerName',
    title: '销售客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'commercialLevel',
    title: '商业级别',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'group',
    title: '集团',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'subsidiaryCompany',
    title: '权属公司',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'systems',
    title: '系统',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'salesArea',
    title: '销售片区',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'provincialCompany',
    title: '省公司',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },


  {
    resizable: true,
    dataIndex: 'office',
    title: '办事处',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },

  {
    resizable: true,
    dataIndex: 'validCustomerCode',
    title: '有效客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'validCustomer',
    title: '有效客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchasingCustomerCode',
    title: '购入客户编号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchasingCustomer',
    title: '购入客户',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'terminalName',
    title: '购入客户协议名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'flowOriginalName',
    title: '流向原始名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'sheng',
    title: '省份',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'productName',
    title: '产品名称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'productSpecification',
    title: '品规全称',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'salesType',
    title: '销售类型',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '现款', value: 1 },
        { key: 2, label: '佣金', value: 2 },
        { key: 3, label: '底价', value: 3 },
      ];

      return staticOptions.filter((x) => x.value === record.salesType)[0]?.label;
    },
  },
  {
    resizable: true,
    dataIndex: 'purchasingResponsibleJobNumber',
    title: '购入责任人工号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'batchNumber',
    title: '批号',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'purchaseDateRevised',
    title: '进货日期修订',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'assessmentUnitPrice',
    title: '考核单价',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'quantity',
    title: '数量',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'assessmentAmount',
    title: '考核金额',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'agentName',
    title: '代理商',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
  },
  {
    resizable: true,
    dataIndex: 'summaryStatus',
    title: '汇总状态',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '已汇总', value: 1 },
        { key: 2, label: '未汇总', value: 0 },
      ];

      return staticOptions.filter((x) => x.value === record.summaryStatus)[0]?.label;
    },
  },
  {
    resizable: true,
    dataIndex: 'accountingStatus',
    title: '核算状态',
    componentType: 'input',
    sorter: true,
    styleConfig: undefined,
    listStyle: undefined,
    customRender: ({ record }) => {
      const staticOptions = [
        { key: 1, label: '已核算', value: 1 },
        { key: 2, label: '未核算', value: 0 },
      ];

      return staticOptions.filter((x) => x.value === record.accountingStatus)[0]?.label;
    },
  },
];
//表单事件
export const formEventConfigs = {
  0: [
    {
      type: 'circle',
      color: '#2774ff',
      text: '开始节点',
      icon: '#icon-kaishi',
      bgcColor: '#D8E5FF',
      isUserDefined: false,
    },
    {
      color: '#F6AB01',
      icon: '#icon-chushihua',
      text: '初始化表单',
      bgcColor: '#f9f5ea',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  1: [
    {
      color: '#B36EDB',
      icon: '#icon-shujufenxi',
      text: '获取表单数据',
      detail: '(新增无此操作)',
      bgcColor: '#F8F2FC',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  2: [
    {
      color: '#F8625C',
      icon: '#icon-jiazai',
      text: '加载表单',
      bgcColor: '#FFF1F1',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  3: [
    {
      color: '#6C6AE0',
      icon: '#icon-jsontijiao',
      text: '提交表单',
      bgcColor: '#F5F4FF',
      isUserDefined: false,
      nodeInfo: { processEvent: [] },
    },
  ],
  4: [
    {
      type: 'circle',
      color: '#F8625C',
      text: '结束节点',
      icon: '#icon-jieshuzhiliao',
      bgcColor: '#FFD6D6',
      isLast: true,
      isUserDefined: false,
    },
  ],
};
export const formProps: FormProps = {
  labelCol: { span: 20, offset: 0 },
  labelAlign: 'right',
  layout: 'vertical',
  size: 'default',
  rowProps: { gutter: 30 }, //整体行间距
  schemas: [
    {
      key: 'af4b9905fe8746688eddb50923bc6af7',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '基本信息',
      componentProps: {
        defaultValue: '基本信息',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    {
      key: '150ebab21d48424f94bdca2fb0a30dd5',
      field: 'agentName',
      label: '代理商名称',
      type: 'input',
      component: 'Input',
      colProps: { span: 12 },
      defaultValue: '',
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: '',
        placeholder: '请输入代理商名称',
        maxlength: null,
        prefix: '',
        suffix: '',
        addonBefore: '',
        addonAfter: '',
        disabled: false,
        allowClear: false,
        showLabel: true,
        required: false,
        rules: [],
        events: {},
        listStyle: '',
        isSave: false,
        isShow: true,
        scan: false,
        style: { width: '100%' },
      },
    },
    {
      key: '7110f63ef52b4c9cbee7636e04f1173e',
      field: 'amount',
      label: '保证金应缴总额',
      type: 'number',
      component: 'InputNumber',
      colProps: { span: 12 },
      defaultValue: 0,
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: 0,
        min: 0,
        max: 99999999,
        step: 1,
        maxlength: null,
        disabled: false,
        showLabel: true,
        controls: true,
        required: false,
        subTotal: false,
        isShow: true,
        rules: [],
        events: {},
        style: { width: '100%' },
      },
    },
    {
      key: 'c27dfc234ce34b9dad3d7b4aeb3f9471',
      field: 'realAmount',
      label: '保证金实缴总额',
      type: 'number',
      component: 'InputNumber',
      colProps: { span: 12 },
      defaultValue: 0,
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: 0,
        min: 0,
        max: 99999999,
        step: 1,
        maxlength: null,
        disabled: false,
        showLabel: true,
        controls: true,
        required: false,
        subTotal: false,
        isShow: true,
        rules: [],
        events: {},
        style: { width: '100%' },
      },
    },
    {
      key: '0c007fce77da4bb1a1bd0d43050a5ffa',
      field: 'balance',
      label: '保证金余额',
      type: 'number',
      component: 'InputNumber',
      colProps: { span: 12 },
      defaultValue: 0,
      componentProps: {
        width: '100%',
        span: '',
        defaultValue: 0,
        min: 0,
        max: 99999999,
        step: 1,
        maxlength: null,
        disabled: false,
        showLabel: true,
        controls: true,
        required: false,
        subTotal: false,
        isShow: true,
        rules: [],
        events: {},
        style: { width: '100%' },
      },
    },
    {
      key: '7e80bdc5f9d54cf7a30f8634308b7496',
      field: '',
      label: '标题',
      type: 'title',
      component: 'Title',
      colProps: { span: 24 },
      defaultValue: '保证金操作记录',
      componentProps: {
        defaultValue: '保证金操作记录',
        color: '',
        align: 'left',
        fontSize: 18,
        isShow: true,
        style: {},
      },
    },
    // {
    //   key: '866f7caac6174c3cb6277e2bc087a0f6',
    //   label: '保证金操作记录',
    //   field: 'cttDepositRecordList',
    //   type: 'form',
    //   component: 'SubForm',
    //   required: true,
    //   colProps: { span: 24 },
    //   componentProps: {
    //     mainKey: 'cttDepositRecordList',
    //     columns: [
    //       {
    //         key: '13219a6c9fe94178b270a0487ce0d7a1',
    //         title: '协议名称',
    //         dataIndex: 'contractName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入协议名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '4c98fdfa067844cea81667a01ca7f427',
    //         title: '操作类型',
    //         dataIndex: 'dealType',
    //         componentType: 'XjrSelect',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           placeholder: '请选择操作类型',
    //           showLabel: true,
    //           showSearch: false,
    //           isMultiple: false,
    //           clearable: false,
    //           disabled: false,
    //           staticOptions: [
    //             { key: 1, label: 'Option 1', value: 'Option 1' },
    //             { key: 2, label: 'Option 2', value: 'Option 2' },
    //             { key: 3, label: 'Option 3', value: 'Option 3' },
    //           ],
    //           defaultSelect: null,
    //           datasourceType: 'dic',
    //           params: { itemId: '1874738527342800897' },
    //           labelField: 'name',
    //           valueField: 'value',
    //           apiConfig: {
    //             path: 'CodeGeneration/selection',
    //             method: 'GET',
    //             apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    //           },
    //           dicOptions: [],
    //           required: false,
    //           rules: [],
    //           events: {},
    //           isShow: true,
    //           itemId: '1874738527342800897',
    //         },
    //       },
    //       {
    //         key: 'cb533a73b0e04246bdc1683bc6f31dff',
    //         title: '保证金类型',
    //         dataIndex: 'depositType',
    //         componentType: 'XjrSelect',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           placeholder: '请选择保证金类型',
    //           showLabel: true,
    //           showSearch: false,
    //           isMultiple: false,
    //           clearable: false,
    //           disabled: false,
    //           staticOptions: [
    //             { key: 1, label: 'Option 1', value: 'Option 1' },
    //             { key: 2, label: 'Option 2', value: 'Option 2' },
    //             { key: 3, label: 'Option 3', value: 'Option 3' },
    //           ],
    //           defaultSelect: null,
    //           datasourceType: 'dic',
    //           params: { itemId: '1874737536170045441' },
    //           labelField: 'name',
    //           valueField: 'value',
    //           apiConfig: {
    //             path: 'CodeGeneration/selection',
    //             method: 'GET',
    //             apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    //           },
    //           dicOptions: [],
    //           required: false,
    //           rules: [],
    //           events: {},
    //           isShow: true,
    //           itemId: '1874737536170045441',
    //         },
    //       },
    //       {
    //         key: '78d7681e655842278a8007f56bbd0f94',
    //         title: '批准部门名称',
    //         dataIndex: 'approvalDeptName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入批准部门名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: 'ac8eb8fb628749e29f5193ff5389b876',
    //         title: '批准人员名称',
    //         dataIndex: 'approvalUserName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入批准人员名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: '24ff0fd60e884cc89e57cc9bc10c24cc',
    //         title: '批准日期',
    //         dataIndex: 'approvalTime',
    //         componentType: 'DatePicker',
    //         defaultValue: '',
    //         componentProps: {
    //           span: '',
    //           defaultValue: '',
    //           width: '100%',
    //           placeholder: '请选择批准日期',
    //           format: 'YYYY-MM-DD HH:mm:ss',
    //           showLabel: true,
    //           allowClear: true,
    //           disabled: false,
    //           required: false,
    //           isShow: true,
    //           rules: [],
    //           events: {},
    //         },
    //       },
    //       {
    //         key: '92fffeee341a43b89656ccfa72286386',
    //         title: '操作金额',
    //         dataIndex: 'amount',
    //         componentType: 'InputNumber',
    //         defaultValue: 0,
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: 0,
    //           min: 0,
    //           max: 99999999,
    //           step: 1,
    //           maxlength: null,
    //           disabled: false,
    //           showLabel: true,
    //           controls: true,
    //           required: false,
    //           subTotal: false,
    //           isShow: true,
    //           rules: [],
    //           events: {},
    //         },
    //       },
    //       {
    //         key: '48776a9345e245e1a9257aea2e2ba504',
    //         title: '执行日期',
    //         dataIndex: 'executionTime',
    //         componentType: 'DatePicker',
    //         defaultValue: '',
    //         componentProps: {
    //           span: '',
    //           defaultValue: '',
    //           width: '100%',
    //           placeholder: '请选择执行日期',
    //           format: 'YYYY-MM-DD HH:mm:ss',
    //           showLabel: true,
    //           allowClear: true,
    //           disabled: false,
    //           required: false,
    //           isShow: true,
    //           rules: [],
    //           events: {},
    //         },
    //       },
    //       {
    //         key: '5981e8a11b9e49c0a0d824f9730c30d7',
    //         title: '执行人员名称',
    //         dataIndex: 'executionUserName',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入执行人员名称',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       {
    //         key: 'e69a4d52c56a4b82a844e691ec6c5b17',
    //         title: '操作备注',
    //         dataIndex: 'dealRemark',
    //         componentType: 'Input',
    //         defaultValue: '',
    //         componentProps: {
    //           width: '100%',
    //           span: '',
    //           defaultValue: '',
    //           placeholder: '请输入操作备注',
    //           maxlength: null,
    //           prefix: '',
    //           suffix: '',
    //           addonBefore: '',
    //           addonAfter: '',
    //           disabled: false,
    //           allowClear: false,
    //           showLabel: true,
    //           required: false,
    //           rules: [],
    //           events: {},
    //           listStyle: '',
    //           isSave: false,
    //           isShow: true,
    //           scan: false,
    //         },
    //       },
    //       { title: '操作', key: 'action', fixed: 'right', width: '50px' },
    //     ],
    //     span: '24',
    //     preloadType: 'api',
    //     apiConfig: {},
    //     itemId: '',
    //     dicOptions: [],
    //     useSelectButton: false,
    //     buttonName: '选择数据',
    //     showLabel: true,
    //     showComponentBorder: true,
    //     showFormBorder: true,
    //     showIndex: false,
    //     isShow: true,
    //     multipleHeads: [],
    //     isExport: false,
    //     isImport: false,
    //     isListView: false,
    //     viewList: [],
    //   },
    // },
  ],
  showActionButtonGroup: false,
  buttonLocation: 'center',
  actionColOptions: { span: 24 },
  showResetButton: false,
  showSubmitButton: false,
  hiddenComponent: [],
};

//左侧树结构配置
export const treeConfig = {
  id: '',
  name: '',
  type: 1,
  config: [],
  configTip: '',
  isMultiple: false,
};
