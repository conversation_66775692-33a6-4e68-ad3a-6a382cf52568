<template>
  <div class="common_box">
    <a-modal
      :width="400"
      v-model:visible="openModalVisible"
      :title="title"
      :maskClosable="false"
      :bodyStyle="{ padding: '0 10px 10px', height: '350px' }"
      destroyOnClose
      @cancel="handleClose"
      @ok="handleOk"
    >
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        autocomplete="off"
        ref="FormRef"
        layout="vertical"
      >
        <a-form-item label="销售类型" name="productName">
<!--          销售类型 1:现款 2：佣金 3：底价-->
          <a-select allow-clear v-model:value="formState.salesType" style="width: 100%" placeholder="请选择销售类型">
            <a-select-option value="1">现款</a-select-option>
            <a-select-option value="2">佣金</a-select-option>
            <a-select-option value="3">底价</a-select-option>
          </a-select>
        </a-form-item>

      </a-form>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { costExpenseFinal, getCommissionByYearMonths, updateFlowDataList } from '/@/api/performance/terminalFlowDetail';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

const formState = reactive<any>({
  salesType: undefined,
});
const props = defineProps({
  openModalVisible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['update:openModalVisible', 'success']);
const openModalVisible = ref<boolean>(false);
const title = ref<string>('批量修改销售类型');

watch(
  () => props.openModalVisible,
  (val) => {
    openModalVisible.value = val;
    if (val) {
      formState.salesType = undefined;
    }
  },
  {
    immediate: true,
  }
);

// 监听内部弹窗状态变化，同步到父组件
watch(
  () => openModalVisible.value,
  (val) => {
    emit('update:openModalVisible', val);
  }
);

const handleClose = () => {
  openModalVisible.value = false;
}
const handleOk = () => {
  if (!formState.salesType) {
    message.error('请选择销售类型');
    return;
  }
  updateFlowDataList(
    {
      ids: props.data,
      salesType: formState.salesType,
    }
  ).then(res=> {
    if (res) {
      message.success('修改成功');
      openModalVisible.value = false;
      emit('success');
    }
  }).catch(err => {
    openModalVisible.value = false;
  })
}

</script>

<style scoped lang="less">
</style>
