<template>
  <a-modal
    :width="600"
    v-model:visible="importModalVisible"
    :title="props.title"
    :confirm-loading="importModalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleImportOk"
    @cancel="() => (importModalVisible = false)"
    :footer="null"
  >
    <div class="modal_box">
      <a-upload-dragger
        name="file"
        :multiple="false"
        accept=".xlsx, .xls"
        :showUploadList="false"
        action="#"
        :max-count="1"
        :fileList="fileList"
        :customRequest="customUpload"
        :headers="{ Authorization: `Bearer ${getToken()}` }"
      >
        <img src="/src/assets/images/import2.png" />
      </a-upload-dragger>
      <div style="text-align: center; margin-top: 20px">
        <a-button type="primary" @click="downloadTemplate">下载模板</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { getToken } from '/@/utils/auth';
import { ref, watch } from 'vue';
import { getAppEnvConfig } from '/@/utils/env';
import { targetImport } from '/@/api/bdm/agentRepres';
import { useMessage } from '/@/hooks/web/useMessage';
const importModalVisible = ref(false);
const importModalLoading = ref(false);
const fileList = ref<any[]>([]);
const { notification } = useMessage();

const emit = defineEmits(['success']);
const props = defineProps({
  importData: {
    type: Object,
    default: {},
  },
  title: {
    type: String,
    default: '导入',
  },
});
watch(
  () => props.importData,
  (val) => {
    console.log(val);
    importModalVisible.value = val.visible;
    if (val.visible) {
      fileList.value = [];
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
// 下载模板
const downloadTemplate = () => {
  // 使用 fetch 获取文件 Blob，然后创建下载链接
  // const url: any = getAppEnvConfig().VITE_GLOB_AGENT_TEMPLATE_URL;
  const url: any = props.importData.downLoadUrl;
  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      const blobURL = window.URL.createObjectURL(blob);
      const tempLink = document.createElement('a');
      tempLink.style.display = 'none';
      tempLink.href = blobURL;
      tempLink.setAttribute('download', props.importData.title + '.xlsx');
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank');
      }
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      window.URL.revokeObjectURL(blobURL);
    });
};
const customUpload = (file) => {
  const formData = new FormData();
  formData.append('file', file.file);
  let action = getAppEnvConfig().VITE_GLOB_API_URL_PREFIX + props.importData.importUrl;
  const xhr = new XMLHttpRequest();
  xhr.open('post', action, true);
  xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
  xhr.onload = () => {
    const res = JSON.parse(xhr.response);
    console.log(res);
    if (res.code == 0) {
      notification.success({
        message: 'Tip',
        description: '导入成功',
      });
      importModalVisible.value = false;
      // getList(); // 刷新列表
      emit('success');
    } else {
      notification.error({
        message: 'Tip',
        description: res.msg,
      });
    }
  };
  xhr.onerror = () => {
    console.log('上传失败');
  };
  xhr.ontimeout = function timeout() {
    console.log('上传超时');
  };
  xhr.send(formData);
};
// 处理导入确认
const handleImportOk = () => {
  try {
    importModalLoading.value = true;
    // 处理导入逻辑
    // 这里可以调用 API 进行文件上传和数据处理
    const formData = new FormData();
    console.log(fileList.value);
    formData.append('file', fileList.value[0]);
    targetImport(formData).then((res: any) => {
      if (res.code == 200) {
        notification.success({
          message: 'Tip',
          description: '导入成功',
        });
        importModalVisible.value = false;
        // getList(1); // 刷新列表
        emit('success')
      }
    });
  } catch (error) {
    console.log(error);
  } finally {
    importModalLoading.value = false;
  }
};
</script>

<style scoped lang="less">

</style>
