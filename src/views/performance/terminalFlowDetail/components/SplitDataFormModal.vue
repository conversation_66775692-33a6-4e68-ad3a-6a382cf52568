<template>
  <div class="common_box">
    <a-modal
      :width="900"
      v-model:visible="openModalVisible"
      :title="title"
      :maskClosable="false"
      :bodyStyle="{ padding: '0 10px 10px' }"
      destroyOnClose
      @cancel="handleClose"
      @ok="handleOk"
    >
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
        autocomplete="off"
        ref="FormRef"
        layout="horizontal"
      >
        <a-row>
          <a-col :span="12">
            <a-form-item label="年月" name="yearMonths">
              {{formState.yearMonths}}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品名称" name="productName">
              {{formState.productName}}
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="销售客户" name="customerName">
              {{formState.customerName}}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="品规全称" name="productSpecification">
              {{formState.productSpecification}}
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="批号" name="batchNumber">
              {{formState.batchNumber}}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="原始总数量" name="quantity">
              {{formState.quantity}}
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="考核单价" name="assessmentUnitPrice">
              {{formState.assessmentUnitPrice}}
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="原始考核金额" name="assessmentAmount">
              {{formState.assessmentAmount}}
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>


      <a-table
        :columns="columns"
        :data-source="data"
        :pagination="false"
        size="small"
        rowKey="id"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'salesType'">
            <a-select placeholder="请选择" v-model:value="record.salesType" style="width: 100%">
              // 1:现款 2：佣金 3：底价
              <a-select-option value="1">现款</a-select-option>
              <a-select-option value="2">佣金</a-select-option>
              <a-select-option value="3">底价</a-select-option>
            </a-select>
          </template>
          <template v-if="column.dataIndex === 'quantity'">
            <a-input-number placeholder="请输入数量" v-model:value="record.quantity" style="width: 100%"></a-input-number>
          </template>
          <template v-if="column.dataIndex === 'assessmentAmount'">
              {{ record?.quantity ? multiply( record?.quantity , formState.assessmentUnitPrice) : 0}}
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-input placeholder="请输入备注" v-model:value="record.remark" style="width: 100%"></a-input>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <icon style="cursor: pointer" icon="ant-design:delete-outlined" color="red" @click="handleDelete(index)"></icon>
          </template>
        </template>
      </a-table>

      <a-button type="primary" style="margin-bottom: 8px" @click="handleAdd">
        <template #icon><Icon icon="ant-design:plus-outlined" /></template>
        添加行
      </a-button>

      <a-space direction="vertical" style="width: 100%" v-if="data.length > 0">
        <a-alert show-icon :message="'当前拆分总数量：'+ totalQuantity + '，与原始数量'+ (totalQuantity === formState.quantity ? '一致' : '不一致')" :type="totalQuantity === formState.quantity ? 'success' : 'error'">
        </a-alert>
      </a-space>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import Icon from '/@/components/Icon';
import { message } from 'ant-design-vue';
import { multiply } from '/@/utils';
import { splitFlowData } from '/@/api/performance/terminalFlowDetail';

const formState = reactive<any>({
  yearMonths: '',
  productName: '',
  customerName: '',
  productSpecification: '',
  batchNumber: '',
  quantity:'',
  assessmentAmount:'',
  assessmentUnitPrice: ''
});
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: 'center',
    customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
  },
  {
    title: '销售类型',
    dataIndex: 'salesType',
    key: 'salesType',
    slot: 'salesType',
    align: 'center',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    slot: 'quantity',
    align: 'center',
    width: 150,
  },
  {
    title: '考核金额',
    dataIndex: 'assessmentAmount',
    key: 'assessmentAmount',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    slot: 'remark',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
  }
];
const data = ref<any[]>([]);
const props = defineProps({
  openModalVisible: {
    type: Boolean,
    default: false,
  },
  flowData: {
    type: Object,
    default: () => {},
  },
});

const emit = defineEmits(['update:openModalVisible', 'success']);

const openModalVisible = ref<boolean>(false);
const totalQuantity = ref<number>(0);

watch(
  () => props.openModalVisible,
  (val) => {
    data.value = [];
    openModalVisible.value = val;
    emit('update:openModalVisible', val);
    if (val) {
      formState.yearMonths = props.flowData.yearMonths;
      formState.productName = props.flowData.productName;
      formState.customerName = props.flowData.customerName;
      formState.productSpecification = props.flowData.productSpecification;
      formState.batchNumber = props.flowData.batchNumber;
      formState.quantity = props.flowData.quantity;
      formState.assessmentUnitPrice = props.flowData.assessmentUnitPrice;
      formState.assessmentAmount = props.flowData.assessmentAmount;
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => data.value,
  (val) => {
    if (val.length > 0) {
      totalQuantity.value = val.reduce((acc, cur) => acc + (cur?.quantity ?? 0), 0);
      data.value.forEach(item => {
        item.assessmentAmount = multiply(item.quantity, formState.assessmentUnitPrice);
      })
    }
  },
  {
    deep: true,
  }
)

const title = ref<string>('拆分流向');

const handleClose = () => {
  openModalVisible.value = false;
}
const handleAdd = () => {
  data.value.push({
    salesType: undefined,
    quantity: '',
    assessmentAmount: '',
    remark: '',
    id: props.flowData.id,
  });
}
const handleDelete = (index) => {
  data.value.splice(index, 1);
}
const handleOk = () => {
  console.log(data.value);
  if (data.value.length === 0) {
    message.error('请添加数据');
    return;
  }
  // 判断销售类型是否为空
  if (data.value.some(item => !item.salesType)) {
    message.error('销售类型不能为空');
    return;
  }
  // 判断数量是否为空
  if (data.value.some(item => !item.quantity)) {
    message.error('数量不能为空');
    return;
  }
  if (totalQuantity.value !== formState.quantity) {
    message.error('拆分总数量与原始数量不一致');
    return;
  }
  console.log(data.value);
  splitFlowData(data.value
  ).then(res=> {
    console.log(res);
    if (res) {
      message.success('拆分成功');
      openModalVisible.value = false;
      emit('success');
    }
  })
}
</script>

<style scoped lang="less">

</style>
