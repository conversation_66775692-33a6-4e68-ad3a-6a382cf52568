<template>

    <BasicModal v-bind="$attrs" @register="registerModal" title="流向汇总" @ok="handleSubmit" @cancel="handleClose" :paddingRight="15" :bodyStyle="{ minHeight: '400px !important' }">
      <ModalForm ref="formRef" :fromPage="FromPageType.MENU" :isView="state.isView"
      :depositId="state.rowId">
      </ModalForm>

    </BasicModal>
  
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, provide, Ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import ModalForm from './Form.vue';
  import { FromPageType } from '/@/enums/workflowEnum';
  import dayjs from 'dayjs';
  import { summaryFlowData } from '/@/api/performance/terminalFlowDetail';

  const emit = defineEmits(['success', 'register']);

  const { notification } = useMessage();
  const formRef = ref();
  const isCopy = ref<boolean>(false)
  const state = reactive({
    formModel: {},
    isUpdate: true,
    isView: false,
    rowId: '',
    yearMonths: ''
  });
  provide<Ref<boolean>>('isCopy', isCopy);

  const { t } = useI18n();
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    state.isUpdate = !!data?.isUpdate;
    state.isView = !!data?.isView;
    isCopy.value = !!data?.isCopy;
    state.yearMonths = data.yearMonths
    console.log(state);
    setModalProps({
      destroyOnClose: true,
      maskClosable: false,
      showCancelBtn: !state.isView,
      showOkBtn: !state.isView,
      canFullscreen: false, //是否可以放大缩小 禁用放大缩小
      width: 410,
      minHeight: 350,
      useWrapper: true, //是否开启自适应高度
      defaultFullscreen: false, //默认全屏
    });
    if (state.isUpdate || state.isView || isCopy.value) {
      console.log('yearMonths', dayjs(state.yearMonths).format('YYYY-MM'));

     if (state.yearMonths) {
        await formRef.value.setFieldsValue({
          yearMonths: dayjs(state.yearMonths).format('YYYY-MM')
        })
     }
    } else {
      formRef.value.resetFields();
    }
  });

  // const getTitle = computed(() => (state.isView ? '查看' : state.isUpdate ? '编辑' : isCopy.value ? '复制数据' : '新增'));


  async function handleSubmit() {
    try {
      console.log('yearMonths11', state.yearMonths);
      await summaryFlowData({
        yearMonths: state.yearMonths,
      }).then(res=> {
        if (res.code === 0) {
            notification.success({
              message: 'Tip',
              description:  '汇总成功',
            }); //提示消息
          closeModal();
          emit('success');
        }
      });

    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
    
  function handleClose() {
    formRef.value.resetFields();
  }

</script>
