<template>
  <div class="common_box">
    <a-modal
      :width="400"
      v-model:visible="openModalVisible"
      :title="title"
      :maskClosable="false"
      :bodyStyle="{ padding: '0 10px 10px', height: '350px' }"
      destroyOnClose
      @cancel="handleClose"
      @ok="handleOk"
    >
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        autocomplete="off"
        ref="FormRef"
        layout="vertical"
      >
        <a-form-item label="选择月份" name="yearMonths">
          <a-date-picker
            v-model:value="formState.yearMonths"
            picker="month"
            placeholder="选择年月"
            style="width: 100%"
            value-format="YYYY-MM"
            disabled
          >
          </a-date-picker>
        </a-form-item>
        <a-form-item label="核算状态" name="productName">
          <a-select value="0" style="width: 100%" disabled>
            <a-select-option value="1">已核算</a-select-option>
            <a-select-option value="0">未核算</a-select-option>
          </a-select>
        </a-form-item>

      </a-form>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { costExpenseFinal, getCommissionByYearMonths } from '/@/api/performance/terminalFlowDetail';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

const formState = reactive<any>({
});
const props = defineProps({
  openModalVisible: {
    type: Boolean,
    default: false,
  },
  flowData: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['update:openModalVisible', 'success']);
const openModalVisible = ref<boolean>(false);
const title = ref<string>('费用核算');

watch(
  () => props.openModalVisible,
  (val) => {
    console.log(val, 'val');
    openModalVisible.value = val;
    if (val) {
      getCommissionByYearMonths().then(res => {
        console.log(res);
        formState.yearMonths = dayjs(res).format('YYYY-MM');
      });
    }
  },
  {
    immediate: true,
  }
);

// 监听内部弹窗状态变化，同步到父组件
watch(
  () => openModalVisible.value,
  (val) => {
    emit('update:openModalVisible', val);
  }
);

const handleClose = () => {
  openModalVisible.value = false;
}
const handleOk = () => {
  costExpenseFinal( {
    accountingMonth: formState.yearMonths
  }).then(res=> {
    if (res) {
      message.success('核算成功');
      openModalVisible.value = false;
      emit('success');
    }
  }).catch(err => {
    openModalVisible.value = false;
  })
}

</script>

<style scoped lang="less">
</style>
