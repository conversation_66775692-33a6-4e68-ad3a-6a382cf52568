<template>
  <ResizePageWrapper :hasLeft="false">
    <template #resizeRight>
      <BasicTable
        @register="registerTable"
        isMenuTable ref="tableRef"
        :row-selection="{ selectedRowKeys: selectedKeys, onChange: onSelectChange }"
      >
<!--        <template #headerCell="{ column }">-->
<!--          <template v-if="column.field === 'yearMonths'">-->
<!--            <div style="display: flex; flex-direction: column; align-items: center;">-->
<!--              <span style="margin-bottom: 8px;">{{ column.title }}</span>-->
<!--              <a-date-picker-->
<!--                v-model:value="selectedYearMonth"-->
<!--                picker="month"-->
<!--                placeholder="选择年月"-->
<!--                style="width: 120px;"-->
<!--                @change="handleYearMonthChange"-->
<!--                :allowClear="true"-->
<!--              />-->
<!--            </div>-->
<!--          </template>-->
<!--        </template>-->

        <template #toolbar>
          <template v-for="button in tableButtonConfig" :key="button.code">
            <a-button
              v-if="button.isDefault"
              type="primary"
              @click="buttonClick(button.code)"
            >
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
            <a-button v-else type="primary">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="getActions(record)" />
          </template>

          <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
          </template>
          <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
          </template>
        </template>
      </BasicTable>
    </template>
    <TerminalFlowDetailModal @register="registerModal" @success="handleSuccess" />
    <ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="columns"
      @success="handleExportSuccess"
    />



    <ImportModal
      @register="registerImportModal"
      importUrl="/pmm/crmZsdlFlowdata/importNewFlowData"
      @success="handleImportSuccess"
    />

    <TeminalFlowSummaryModal @register="registerSummaryModal" @success="handleSuccess" >

    </TeminalFlowSummaryModal>
    <!--    <DepositrecordModal @register="registerDepositrecordModal" @success="handleSuccess" />-->
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';

  import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
  import { getInvoicingPage, exportInvoicing } from '/@/api/performance/invoicing';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { executeListStyle } from '/@/hooks/web/useListStyle';
  import { useRouter } from 'vue-router';

  import { useModal } from '/@/components/Modal';

  import TerminalFlowDetailModal from './components/TerminalFlowDetailModal.vue';

  import { downloadByData } from '/@/utils/file/download';
  import ExportModal from '/@/views/form/template/components/ExportModal.vue';

  import { searchFormSchema, columns } from './components/config';
  // import DepositrecordModal from '/@/views/bdm/depositrecord/components/DepositrecordModal.vue';
  import Icon from '/@/components/Icon/index';
  import { ImportModal } from '/@/components/Import';
  import { deleteFlowdata, getCustomFlowPage, getFlowPage } from '/@/api/performance/terminalFlowDetail';
  import TeminalFlowSummaryModal from './components/TerminalFlowSummaryModal.vue';

  const [registerDepositrecordModal, { openModal: openDepositrecordModal }] = useModal();

  const [registerSummaryModal, { openModal: openSummaryModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  const filterColumns = filterColumnAuth(columns);
  const tableRef = ref();
  const pageParamsInfo = ref<any>({});

  const visibleExport = ref(false);

  const selectedKeys = ref<string[]>([]);
  const selectedRowsData = ref<any[]>([]);

  // 年月选择器的值
  const selectedYearMonth = ref(null);
  

  //展示在列表内的按钮
  const actionButtons = ref<string[]>([
    'view',
    'edit',
    'delete',
    'split'
  ]);
  const buttonConfigs = computed(() => {
    const list = [
      { isUse: true, name: '查看', code: 'view', icon: 'ant-design:eye-outlined', isDefault: true },
      {
        isUse: true,
        name: '导入新流向',
        code: 'import',
        icon: 'ant-design:import-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '导入修订表',
        code: 'importEdit',
        icon: 'ant-design:import-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '流向汇总',
        code: 'summary',
        icon: 'ant-design:control-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '费用核算',
        code: 'account',
        icon: 'ant-design:calculator-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '批量修改销售类型',
        code: 'batchEdit',
        icon: 'ant-design:edit-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '批量删除',
        code: 'batchDelete',
        icon: 'ant-design:delete-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '导出',
        code: 'export',
        icon: 'ant-design:export-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '拆分',
        code: 'split',
        icon: 'ant-design:scissor-outlined',
        isDefault: true,
      },
      {
        isUse: true,
        name: '删除',
        code: 'delete',
        icon: 'ant-design:delete-outlined',
        isDefault: true,
      },
    ];
    return filterButtonAuth(list);
  });

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const btnEvent = {
    view: handleView,
    export: handleExport,
    import: handleImport,
    split: handleSplit,
    delete: handleDelete,
    batchDelete: handleBatchDelete,
    summary: handleSummary,
  };

  const { currentRoute } = useRouter();

  const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

  const [registerModal, { openModal }] = useModal();

  const [registerTable, { reload }] = useTable({
    title: '终端消化流向明细',
    api: getFlowPage,
    rowKey: 'id',
    columns: columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: true,
    },
    beforeFetch: (params) => {
      pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
      return pageParamsInfo.value;
    },
    afterFetch: (res) => {
      tableRef.value.setToolBarWidth();
    },
    useSearchForm: true,
    showTableSetting: true,

    striped: false,
    tableSetting: {
      size: false,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    customRow,
    isAdvancedQuery: false,
    querySelectOption: JSON.stringify(searchFormSchema),
    objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
  });

  function buttonClick(code) {
    btnEvent[code]();
  }

  // 处理年月选择器变化
  function handleYearMonthChange(value) {
    selectedYearMonth.value = value;
    // 这里可以添加额外的逻辑，比如重新加载表格数据
    console.log('选中的年月:', value);
  }

  function customRow(record: Recordable) {
    return {
      ondblclick: () => {
        // if (record.isCanEdit && hasPermission('deposit:edit')) {
        //   handleEdit(record);
        // }
      },
    };
  }

  function handleSuccess() {
    reload();
  }

  function handleView(record: Recordable) {
    openModal(true, {
      isView: true,
      id: record.id,
    });
  }

  async function handleExport() {
    // visibleExport.value = true;
    handleExportSuccess('');
  }

  function handleImportSuccess() {
    reload();
  }

  async function handleExportSuccess(cols) {
    const res = await exportInvoicing({
      isTemplate: false,
      columns: cols.toString(),
      ...pageParamsInfo.value,
    });
    visibleExport.value = false;
    downloadByData(
      res.data,
      '开票数据.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  function getActions(record: Recordable): ActionItem[] {
    record.isCanEdit = false;

    const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        return {
          icon: button?.icon,
          // auth: `terminalFlowDetail:${button.code}`, // 临时注释权限检查
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        if (button.code === 'view') {
          return {
            icon: button?.icon,
            // auth: `terminalFlowDetail:${button.code}`, // 临时注释权限检查
            tooltip: button?.name,
            onClick: btnEvent[button.code].bind(null, record),
          };
        } else {
          return {};
        }
      }
    });
    return actionsList;
  }

  function handleImport() {
    openImportModal(true, {
      title: '快速导入',
      downLoadUrl: '/pmm/crmZsdlFlowdata/importNewFlowData',
    });

    // openImportModal(true, {
    //   title: '快速导入',
    //   downLoadUrl: '/base/agentevalindicator/export',
    // });
  }

  function handleSummary() {
    openSummaryModal(true, {
      isUpdate: false,
    });
  }
  
  function handleSplit(record) {
    console.log('拆分操作', record);
    // TODO: 实现拆分逻辑
  }

  function handleDelete(record) {
    console.log('删除操作', record);
    // TODO: 实现删除逻辑
  }
  function handleBatchDelete() {
    if (selectedKeys.value.length == 0) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要删除的数据'),
      });
      return;
    }

    //与工作流相关的数据不能进行批量删除
    const cantDelete = selectedRowsData.value.filter((x) => {
      return (
        (x.workflowData?.enabled && x.workflowData?.status) ||
        (!x.workflowData?.enabled && !!x.workflowData?.processId)
      );
    });
    if (cantDelete.length) {
      notification.warning({
        message: 'Tip',
        description: t('含有不能删除的数据'),
      });
      return;
    }
    deleteFlowdata(selectedKeys.value);
  }

  function onSelectChange(selectedRowKeys: [], selectedRows) {
    selectedKeys.value = selectedRowKeys;
    selectedRowsData.value = selectedRows;
  }

  // function handleImportSuccess() {
  //   reload();
  // }
  //
  // function handleChange(record: Recordable) {
  //   openDepositrecordModal(true, {
  //     isUpdate: false,
  //     depositId: record.id,
  //     agentId: record.agentId,
  //     agentName: record.agentName,
  //   });
  // }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }

  .show {
    display: flex;
  }

  .hide {
    display: none !important;
  }
</style>
