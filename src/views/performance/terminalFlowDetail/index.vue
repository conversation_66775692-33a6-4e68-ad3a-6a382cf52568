<template>
  <ResizePageWrapper :hasLeft="false">
    <template #resizeRight>
      <BasicTable @register="registerTable" isMenuTable ref="tableRef">
        <template #toolbar>
          <template v-for="button in tableButtonConfig" :key="button.code">
            <a-button
              v-if="button.isDefault"
              type="primary"
              v-auth="`terminalflow:${button.code}`"
              @click="buttonClick(button.code)"
            >
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
            <a-button v-else type="primary">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
          </template>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="getActions(record)" />
          </template>

          <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
          </template>
          <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
          </template>
        </template>
      </BasicTable>
    </template>
<!--    <template #resizeRight>-->
<!--      <BasicTable @register="registerTable" isMenuTable ref="tableRef">-->
<!--&lt;!&ndash;        <template #headerCell="{ column }">&ndash;&gt;-->
<!--&lt;!&ndash;          <template v-if="column.dataIndex === 'yearMonths'">&ndash;&gt;-->
<!--&lt;!&ndash;            <div style="display: flex; flex-direction: column; align-items: center;">&ndash;&gt;-->
<!--&lt;!&ndash;              <span style="margin-bottom: 8px;">{{ column.title }}</span>&ndash;&gt;-->
<!--&lt;!&ndash;              <a-date-picker&ndash;&gt;-->
<!--&lt;!&ndash;                v-model:value="selectedYearMonth"&ndash;&gt;-->
<!--&lt;!&ndash;                picker="month"&ndash;&gt;-->
<!--&lt;!&ndash;                placeholder="选择年月"&ndash;&gt;-->
<!--&lt;!&ndash;                style="width: 120px;"&ndash;&gt;-->
<!--&lt;!&ndash;                @change="handleYearMonthChange"&ndash;&gt;-->
<!--&lt;!&ndash;                :allowClear="true"&ndash;&gt;-->
<!--&lt;!&ndash;              />&ndash;&gt;-->
<!--&lt;!&ndash;            </div>&ndash;&gt;-->
<!--&lt;!&ndash;          </template>&ndash;&gt;-->
<!--&lt;!&ndash;        </template>&ndash;&gt;-->
<!--        <template #toolbar>-->
<!--          <template v-for="button in tableButtonConfig" :key="button.code">-->
<!--            <a-button-->
<!--              v-if="button.isDefault"-->
<!--              type="primary"-->
<!--              v-auth="`terminalflow:${button.code}`"-->
<!--              @click="buttonClick(button.code)"-->
<!--            >-->
<!--              <template #icon>-->
<!--                <Icon :icon="button.icon" />-->
<!--              </template>-->
<!--              {{ button.name }}-->
<!--            </a-button>-->
<!--            <a-button v-else type="primary">-->
<!--              <template #icon>-->
<!--                <Icon :icon="button.icon" />-->
<!--              </template>-->
<!--              {{ button.name }}-->
<!--            </a-button>-->
<!--          </template>-->
<!--        </template>-->
<!--        <template #bodyCell="{ column, record }">-->
<!--          <template v-if="column.dataIndex === 'action'">-->
<!--            <TableAction :actions="getActions(record)" />-->
<!--          </template>-->

<!--          <template v-else-if="column.staticOptions?.length">-->
<!--            <span :style="executeListStyle(record, column?.listStyle)">-->
<!--              {{-->
<!--                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label-->
<!--              }}-->
<!--            </span>-->
<!--          </template>-->
<!--          <template v-else-if="column.dataIndex && column?.listStyle">-->
<!--            <span :style="executeListStyle(record, column?.listStyle)">{{-->
<!--              record[column.dataIndex]-->
<!--            }}</span>-->
<!--          </template>-->
<!--        </template>-->
<!--      </BasicTable>-->
<!--    </template>-->

    <TerminalFlowDetailModal @register="registerModal" @success="handleSuccess" />
    <ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="columns"
      @success="handleExportSuccess"
    />
    <!--    <DepositrecordModal @register="registerDepositrecordModal" @success="handleSuccess" />-->
  </ResizePageWrapper>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';

  import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
  import { getInvoicingPage, exportInvoicing } from '/@/api/performance/invoicing';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { executeListStyle } from '/@/hooks/web/useListStyle';
  import { useRouter } from 'vue-router';

  import { useModal } from '/@/components/Modal';

  import TerminalFlowDetailModal from './components/TerminalFlowDetailModal.vue';

  import { downloadByData } from '/@/utils/file/download';
  import ExportModal from '/@/views/form/template/components/ExportModal.vue';

  import { searchFormSchema, columns } from './components/config';
  // import DepositrecordModal from '/@/views/bdm/depositrecord/components/DepositrecordModal.vue';
  const [registerDepositrecordModal, { openModal: openDepositrecordModal }] = useModal();
  import Icon from '/@/components/Icon/index';
  import { ImportModal } from '/@/components/Import';
  import { getFlowPage } from '/@/api/performance/terminalFlowDetail';

  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  const filterColumns = filterColumnAuth(columns);
  const tableRef = ref();
  const pageParamsInfo = ref<any>({});

  const visibleExport = ref(false);

  // 年月选择器的值
  const selectedYearMonth = ref(null);

  //展示在列表内的按钮
  const actionButtons = ref<string[]>([
    'view',
    'edit',
    'copyData',
    'delete',
    'change',
    'startwork',
    'flowRecord',
    'pushorder',
    'import'
  ]);
  const buttonConfigs = computed(() => {
    const list = [
      { isUse: true, name: '查看', code: 'view', icon: 'ant-design:eye-outlined', isDefault: true },
      {
        isUse: true,
        name: '导入新流向',
        code: 'import',
        icon: 'ant-design:import-outlined',
        isDefault: true,
      },
    ];
    return filterButtonAuth(list);
  });

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const btnEvent = {
    view: handleView,
    export: handleExport,
    import: handleImport,
  };

  const { currentRoute } = useRouter();

  const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

  const [registerModal, { openModal }] = useModal();

  const [registerTable, { reload }] = useTable({
    title: '开票数据列表',
    api: getFlowPage,
    rowKey: 'id',
    columns: filterColumns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: true,
    },
    beforeFetch: (params) => {
      pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
      return pageParamsInfo.value;
    },
    afterFetch: (res) => {
      tableRef.value.setToolBarWidth();
    },
    useSearchForm: true,
    showTableSetting: true,

    striped: false,
    // actionColumn: {
    //   width: 160,
    //   title: '操作',
    //   dataIndex: 'action',
    //   slots: { customRender: 'action' },
    // },
    tableSetting: {
      size: false,
    },
    customRow,
    isAdvancedQuery: false,
    querySelectOption: JSON.stringify(searchFormSchema),
    objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
  });

  function buttonClick(code) {
    btnEvent[code]();
  }

  // 处理年月选择器变化
  function handleYearMonthChange(value) {
    selectedYearMonth.value = value;
    // 这里可以添加额外的逻辑，比如重新加载表格数据
    console.log('选中的年月:', value);
  }

  function customRow(record: Recordable) {
    return {
      ondblclick: () => {
        // if (record.isCanEdit && hasPermission('deposit:edit')) {
        //   handleEdit(record);
        // }
      },
    };
  }

  function handleSuccess() {
    reload();
  }

  function handleView(record: Recordable) {
    openModal(true, {
      isView: true,
      id: record.id,
    });
  }

  async function handleExport() {
    // visibleExport.value = true;
    handleExportSuccess('');
  }


  async function handleExportSuccess(cols) {
    const res = await exportInvoicing({
      isTemplate: false,
      columns: cols.toString(),
      ...pageParamsInfo.value,
    });
    visibleExport.value = false;
    downloadByData(
      res.data,
      '开票数据.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }

  function getActions(record: Recordable): ActionItem[] {
    record.isCanEdit = false;

    const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        return {
          icon: button?.icon,
          auth: `invoicing:${button.code}`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        if (button.code === 'view') {
          return {
            icon: button?.icon,
            auth: `invoicing:${button.code}`,
            tooltip: button?.name,
            onClick: btnEvent[button.code].bind(null, record),
          };
        } else {
          return {};
        }
      }
    });
    return actionsList;
  }

  function handleImport() {
    openImportModal(true, {
      title: '快速导入',
      downLoadUrl: '/pmm/crmZsdlFlowdata/importNewFlowData',
    });

    // openImportModal(true, {
    //   title: '快速导入',
    //   downLoadUrl: '/base/agentevalindicator/export',
    // });
  }

  function handleImportSuccess() {
    reload();
  }

  function handleChange(record: Recordable) {
    openDepositrecordModal(true, {
      isUpdate: false,
      depositId: record.id,
      agentId: record.agentId,
      agentName: record.agentName,
    });
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }

  .show {
    display: flex;
  }

  .hide {
    display: none !important;
  }
</style>
