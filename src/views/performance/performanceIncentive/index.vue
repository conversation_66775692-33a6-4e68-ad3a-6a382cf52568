<template>
<!--  <ResizePageWrapper :hasLeft="false">-->
  <div class="common_box">
      <a-tabs>
        <a-tab-pane key="1" tab="月度业绩达成激励汇总表" >
          <BasicTable @register="registerMonthTable" isMenuTable ref="tableRef">
            <template #toolbar>
              <template v-for="button in tableButtonConfig" :key="button.code">
                <a-button
                  v-if="button.isDefault"
                  type="primary"
                  v-auth="`invoicing:${button.code}`"
                  @click="buttonClick(button.code)"
                >
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
                <a-button v-else type="primary">
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
              </template>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getActions(record)" />
              </template>

              <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
              </template>
              <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
        <a-tab-pane key="2" tab="季度业绩达成激励汇总表" >
          <BasicTable @register="registerQuarterTable" isMenuTable ref="tableRef">
            <template #toolbar>
              <template v-for="button in tableButtonConfig" :key="button.code">
                <a-button
                  v-if="button.isDefault"
                  type="primary"
                  v-auth="`invoicing:${button.code}`"
                  @click="buttonClick(button.code)"
                >
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
                <a-button v-else type="primary">
                  <template #icon><Icon :icon="button.icon" /></template>
                  {{ button.name }}
                </a-button>
              </template>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getActions(record)" />
              </template>

              <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
              </template>
              <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
                record[column.dataIndex]
              }}</span>
              </template>
            </template>
          </BasicTable>
        </a-tab-pane>
        <a-tab-pane key="3" tab="年度业绩达成激励汇总表" >

        </a-tab-pane>
      </a-tabs>

    <PerformanceIncentiveModal @register="registerModal" @success="handleSuccess" />
    <ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="monthColumns"
      @success="handleExportSuccess"
    />
    <DepositrecordModal @register="registerDepositrecordModal" @success="handleSuccess" />
<!--  </ResizePageWrapper>-->
<!--    </template>-->
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';

import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
import { getInvoicingPage, exportInvoicing } from '/@/api/performance/invoicing';
import { ResizePageWrapper } from '/@/components/Page';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { usePermission } from '/@/hooks/web/usePermission';
import { executeListStyle } from '/@/hooks/web/useListStyle';
import { useRouter } from 'vue-router';

import { useModal } from '/@/components/Modal';

import PerformanceIncentiveModal from './components/PerformanceIncentiveModal.vue';

import { downloadByData } from '/@/utils/file/download';
import ExportModal from '/@/views/form/template/components/ExportModal.vue';

import { searchFormSchema, monthColumns, quarterColumns } from './components/config';
import DepositrecordModal from '/@/views/bdm/depositrecord/components/DepositrecordModal.vue';
const [registerDepositrecordModal, { openModal: openDepositrecordModal }] = useModal();
import Icon from '/@/components/Icon/index';
import { ImportModal } from '/@/components/Import';
import { getMonthPage } from '/@/api/performance/PerformanceIncentive';
const [registerImportModal, { openModal: openImportModal }] = useModal();
const { notification } = useMessage();
const { t } = useI18n();
defineEmits(['register']);
const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

// const filterColumns = filterColumnAuth(columns);
const tableRef = ref();
const pageParamsInfo = ref<any>({});

const visibleExport = ref(false);

//展示在列表内的按钮
const actionButtons = ref<string[]>([
  'view',
  'edit',
  'copyData',
  'delete',
  'change',
  'startwork',
  'flowRecord',
  'pushorder',
]);
const buttonConfigs = computed(() => {
  const list = [
    { isUse: true, name: '查看', code: 'view', icon: 'ant-design:eye-outlined', isDefault: true },
    {
      isUse: true,
      name: '快速导出',
      code: 'export',
      icon: 'ant-design:export-outlined',
      isDefault: true,
    },
  ];
  return filterButtonAuth(list);
});

const tableButtonConfig = computed(() => {
  return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
});

const actionButtonConfig = computed(() => {
  return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
});

const btnEvent = {
  view: handleView,
  export: handleExport,
};

const { currentRoute } = useRouter();

const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

const [registerModal, { openModal }] = useModal();

const [registerMonthTable, { reload: reloadMonthTable }] = useTable({
  title: '',

  api: getMonthPage,
  rowKey: 'id',
  columns: filterColumnAuth(monthColumns),
  formConfig: {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormSchema,
    fieldMapToTime: [],
    showResetButton: true,
  },
  beforeFetch: (params) => {
    pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
    return pageParamsInfo.value;
  },
  afterFetch: (res) => {
    tableRef.value.setToolBarWidth();
  },
  useSearchForm: true,
  showTableSetting: true,

  striped: false,
  tableSetting: {
    size: false,
  },
  customRow,
  isAdvancedQuery: false,
  querySelectOption: JSON.stringify(searchFormSchema),
  objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
});

// 季度
const [registerQuarterTable, { reload: reloadQuarterTable }] = useTable({
  title: '月度业绩达成激励汇总表',
  api: getMonthPage,
  rowKey: 'id',
  columns: filterColumnAuth(quarterColumns),
  formConfig: {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormSchema,
    fieldMapToTime: [],
    showResetButton: true,
  },
  beforeFetch: (params) => {
    pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: 'id' };
    return pageParamsInfo.value;
  },
  afterFetch: (res) => {
    tableRef.value.setToolBarWidth();
  },
  useSearchForm: true,
  showTableSetting: true,

  striped: false,
  tableSetting: {
    size: false,
  },
  customRow,
  isAdvancedQuery: false,
  querySelectOption: JSON.stringify(searchFormSchema),
  objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
});

function buttonClick(code) {
  btnEvent[code]();
}

function customRow(record: Recordable) {
  return {
    ondblclick: () => {
      // if (record.isCanEdit && hasPermission('deposit:edit')) {
      //   handleEdit(record);
      // }
    },
  };
}

function handleSuccess() {
  reloadMonthTable();
  reloadQuarterTable();
}

function handleView(record: Recordable) {
  openModal(true, {
    isView: true,
    id: record.id,
  });
}

async function handleExport() {
  // visibleExport.value = true;
  handleExportSuccess('');
}
async function handleExportSuccess(cols) {
  const res = await exportInvoicing({
    isTemplate: false,
    columns: cols.toString(),
    ...pageParamsInfo.value,
  });
  visibleExport.value = false;
  downloadByData(
    res.data,
    '开票数据.xlsx',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  );
}

function getActions(record: Recordable): ActionItem[] {
  record.isCanEdit = false;

  const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
    if (!record?.workflowData?.processId) {
      record.isCanEdit = true;
      return {
        icon: button?.icon,
        auth: `invoicing:${button.code}`,
        tooltip: button?.name,
        color: button.code === 'delete' ? 'error' : undefined,
        onClick: btnEvent[button.code].bind(null, record),
      };
    } else {
      if (button.code === 'view') {
        return {
          icon: button?.icon,
          auth: `invoicing:${button.code}`,
          tooltip: button?.name,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        return {};
      }
    }
  });
  return actionsList;
}
function handleImport() {
  openImportModal(true, {
    title: '快速导入',
    templateTitle: '保证金操作记录',
    downLoadUrl: '/bdm/deposit/export',
  });
}
function handleImportSuccess() {
  reloadMonthTable();
  reloadQuarterTable();
}

function handleChange(record: Recordable) {
  openDepositrecordModal(true, {
    isUpdate: false,
    depositId: record.id,
    agentId: record.agentId,
    agentName: record.agentName,
  });
}
</script>
<style lang="less" scoped>
.common_box {
  width: 100%;
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  .ant-tabs {
    background-color: #fff;
    padding: 0 16px;
    :deep(> .ant-tabs-nav) {
      margin-bottom: 0 !important;
    }
    height: 100%;
    .ant-tabs-content {
      height: 100%;
    }
  }
  .main_box {
    flex: auto;
    height: 0;
  }
}
:deep(.ant-table-selection-col) {
  width: 50px;
}
.show {
  display: flex;
}
.hide {
  display: none !important;
}
</style>
