<template>
  <div class="common_box">
    <a-tabs v-model:activeKey="activeKey" @change="getList">
      <a-tab-pane :key="1" tab="周计划" />
      <a-tab-pane :key="2" tab="月计划" />
    </a-tabs>
    <div class="main_box">
      <common-Table
        ref="commonTableRef"
        :tableColumns="tableColumns"
        :tableData="tableData"
        :loading="loading"
        :modal-title="activeKey === 1 ? '周计划' : '月计划'"
        :modal-width="650"
        :modalFooter="null"
        @pagination-change="() => getList(1)"
        @get-list="() => getList()"
        @re-set="() => reSet()"
        @handle-modal-ok="() => handleModalOk()"
      >
        <template #filterForm>
          <a-input
            style="width: 240px"
            v-model:value.lazy="searchForm.purMerchantName"
            placeholder="提交人"
            allowClear
            @change="getList()"
            @press-enter="getList()"
          />
          <a-range-picker
            :picker="activeKey === 1 ? 'week' : 'month'"
            v-model:value="searchForm.time"
            :placeholder="['计划周期', '结束时间']"
            @change="getList()"
          />
        </template>
        <template #action="{ record }">
          <a-button type="link" @click.stop="openModal(record)">查看详情</a-button>
        </template>
        <template #modal>
          <p class="modal_p">
            <span>计划周期</span>
            <span class="modal_range"
              >{{ modalInfo.startYearMonthDay }} ~ {{ modalInfo.endYearMonthDay }}</span
            >
          </p>
          <a-table
            :columns="columnsModal"
            :data-source="dataModal"
            :loading="loadingModal"
            :pagination="false"
          />
          <p class="modal_p">其他计划说明</p>
          <div class="modal_tip">{{ modalInfo.remark }}</div>
        </template>
      </common-Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import commonTable from '/@/views/dailyWork/components/commonTable.vue';
  import dayjs from 'dayjs';
  import { debounce } from 'lodash-es';
  import { getTaskInfo, getTaskPage } from '/@/api/bdm/daily';
  const activeKey = ref(1);
  const tableColumns = [
    {
      title: '计划周期',
      dataIndex: 'cycle',
      key: 'cycle',
      align: 'center',
    },
    {
      title: '提交人',
      dataIndex: 'createUserName',
      key: 'createUserName',
      align: 'center',
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
    },
    {
      title: '提交时间',
      dataIndex: 'createDate',
      key: 'createDate',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const searchForm = ref({
    purMerchantName: '',
    time: [],
  });
  const reSet = () => {
    searchForm.value = {
      salesmanNameOrEdpCode: '',
      purMerchantName: '',
      time: [],
    };
    getList();
  };
  const getDate = (date: any, flag: boolean) => {
    if (!date) return '';
    if (activeKey.value === 2) return dayjs(date).format('YYYY-MM');
    if (flag) return dayjs(date).startOf('week').add(1, 'day').format('YYYY-MM-DD');
    else return dayjs(date).endOf('week').add(1, 'day').format('YYYY-MM-DD');
  };

  const commonTableRef = ref();
  const getList = debounce(async (flag?: any) => {
    if (!flag) {
      commonTableRef.value.pagination.currentPage = 1;
      commonTableRef.value.pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.value = [];

    try {
      let temp = {
        createUserName: searchForm.value.purMerchantName,
        limit: commonTableRef.value.pagination.currentPage,
        size: commonTableRef.value.pagination.pageSize,
        frequency: activeKey.value,
      };
      if (searchForm.value.time && searchForm.value.time.length > 0) {
        temp.startYearMonthDay = getDate(searchForm.value.time?.[0], true);
        temp.endYearMonthDay = getDate(searchForm.value.time?.[1], false);
      }

      console.log('getList', temp);

      const res = await getTaskPage(temp);

      commonTableRef.value.pagination.totalItems = res.total ?? 0;
      tableData.value = res?.list ?? [];
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  }, 200);
  const columnsModal = ref([
    {
      title: '重点工作',
      dataIndex: 'taskName',
      key: 'taskName',
      align: 'center',
      width: 200,
    },
    {
      title: '目标',
      dataIndex: 'goal',
      key: 'goal',
      align: 'center',
      width: 200,
    },
  ]);
  const dataModal = ref([
    // {
    //   label: 'preVisitPlan',
    //   title: '代理商拜访',
    //   value: '',
    // },
  ]);
  const modalInfo = ref({});
  const loadingModal = ref(false);
  const openModal = (row) => {
    dataModal.value = [];
    commonTableRef.value.changeModal(true);
    loadingModal.value = true;
    getTaskInfo({ id: row.id }).then((data) => {
      modalInfo.value = data;
      try {
        dataModal.value = data.dbmSalesmanTaskVisitPersonVoList;
        loadingModal.value = false;
      } catch (error) {
        console.log(error);
        loadingModal.value = false;
      }
    });
  };
  const handleModalOk = () => {
    commonTableRef.value.changeModal(false);
  };
  onMounted(() => {
    getList();
  });
</script>

<style scoped lang="less">
  .common_box {
    width: 100%;
    height: 100%;
    padding: 8px;
    display: flex;
    flex-direction: column;
    .ant-tabs {
      background-color: #fff;
      padding: 0 16px;
      :deep(> .ant-tabs-nav) {
        margin-bottom: 0 !important;
      }
    }
    .main_box {
      flex: auto;
      height: 0;
    }
  }
  .filterForm_box {
    > * + * {
      margin-left: 16px;
    }
  }
  .modal_p {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
    &:last-of-type {
      margin-top: 16px;
    }
  }
  .modal_range {
    font-weight: normal;
    color: #333;
    margin-left: 4px;
  }
  .modal_tip {
    padding: 0 16px;
    margin-bottom: 16px;
  }
</style>
