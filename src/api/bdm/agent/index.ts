import { BdmAgentPageModel, BdmAgentPageParams, BdmAgentPageResult } from './model/AgentModel';
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/bdm/agent/page',
  List = '/bdm/agent/list',
  Info = '/bdm/agent/info',
  Add = '/bdm/agent/add',
  Update = '/bdm/agent/update',
  Delete = '/bdm/agent/delete',
  Enable = '/bdm/agent/enable',
  Disable = '/bdm/agent/disable',
  Export = '/bdm/agent/export',
  AgentSync = '/bdm/agent/agentSync',
  getIdCard = '/baidu/ocr/idcard',
  getBusinessLicense = '/baidu/ocr/businessLicense',
}

/**
 * @description: 查询BdmAgent分页列表
 */
export async function getBdmAgentPage(
  params: BdmAgentPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<BdmAgentPageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getIdCard(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.getIdCard,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getBusinessLicense(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.getBusinessLicense,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取BdmAgent信息
 */
export async function getBdmAgent(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<BdmAgentPageModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增BdmAgent
 */
export async function addBdmAgent(bdmAgent: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Add,
      params: bdmAgent,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新BdmAgent
 */
export async function updateBdmAgent(bdmAgent: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Update,
      params: bdmAgent,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除BdmAgent（批量删除）
 */
export async function deleteBdmAgent(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/*
 * 启用
 */
export async function enableList(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Enable,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/*
 * 禁用
 */
export async function disableList(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Disable,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/*
 * 同步
 */
export async function agentSyncList(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<string>(
    {
      url: Api.AgentSync,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出
 */
export async function exportBdmAgent(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
