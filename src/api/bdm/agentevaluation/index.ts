import { BdmAgentEvaluationPageModel, BdmAgentEvaluationPageParams, BdmAgentEvaluationPageResult } from './model/AgentEvaluationModel';
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/bdm/agentevaluation/page',
  reservePage = '/bdm/agentterminalrel/page',
  List = '/bdm/agentevaluation/list',
  Info = '/bdm/agentevaluation/info',
  Add = '/bdm/agentevaluation/add',
  Update = '/bdm/agentevaluation/update',
  Delete = '/bdm/agentevaluation/delete',
  getAgentEvalTemplateSelect = '/base/agentevaltemplate/getAgentEvalTemplateSelect',
  getAgentValuationByAgentId = '/bdm/agentevaluation/getAgentValuationByAgentId',
  getInfoWithEnable = '/base/agentevaltemplate/getInfoWithEnable',

  
  
  Export = '/bdm/agentevaluation/export',
  
}

/**
 * @description: 查询BdmAgentEvaluation分页列表
 */
export async function getBdmAgentEvaluationPage(params: BdmAgentEvaluationPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<BdmAgentEvaluationPageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getAgentEvalTemplateSelect(params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.getAgentEvalTemplateSelect,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getInfoWithEnable(params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.getInfoWithEnable,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getAgentValuationByAgentId(params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.getAgentValuationByAgentId,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取BdmAgentEvaluation信息
 */
export async function getBdmAgentEvaluation(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<BdmAgentEvaluationPageModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增BdmAgentEvaluation
 */
export async function addBdmAgentEvaluation(bdmAgentEvaluation: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Add,
      params: bdmAgentEvaluation,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新BdmAgentEvaluation
 */
export async function updateBdmAgentEvaluation(bdmAgentEvaluation: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Update,
      params: bdmAgentEvaluation,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除BdmAgentEvaluation（批量删除）
 */
export async function deleteBdmAgentEvaluation(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}



/**
 * @description: 导出BdmAgentEvaluation
 */
export async function exportBdmAgentEvaluation(
  params?: object, 
  mode: ErrorMessageMode = 'modal'
) {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
