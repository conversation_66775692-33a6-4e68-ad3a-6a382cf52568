import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/pmm/crmZsdlFlowdata/page',
  CustomPage = '/pmm/flowdataCustom/page',
  Export = '/pmm/b2b/invoice/export',
  Delete = '/pmm/crmZsdlFlowdata',
  ImportNewFlowData = '/pmm/crmZsdlFlowdata/importNewFlowData'
}

export async function getFlowPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getCustomFlowPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.CustomPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function exportInvoicing(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteFlowdata(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function importNewFlowData(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.ImportNewFlowData,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
