import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/pmm/crmZsdlFlowdata/page',
  CustomPage = '/pmm/flowdataCustom/page',
  Export = '/pmm/b2b/invoice/export',
  Delete = '/pmm/crmZsdlFlowdata',
  GetByYearMonths = '/pmm/crmZsdlFlowdata/getByYearMonths',
  SummaryFlowData= '/pmm/crmZsdlFlowdata/summaryFlowData',
  GetByIdFlowData = '/pmm/crmZsdlFlowdata',
  SplitFlowData = '/pmm/crmZsdlFlowdata/splitFlowData',
  GetCommissionByYearMonths = '/pmm/crmZsdlFlowdata/getCommissionByYearMonths',
  CostExpenseFinal = '/pmm/crmZsdlFlowdata/costExpenseFinal',
  PmmExport = '/pmm/crmZsdlFlowdata/pmmExport',
  UpdateFlowDataList = '/pmm/crmZsdlFlowdata/updatePmmCrmZsdlFlowdataList'
}

export async function getFlowPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getCustomFlowPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.CustomPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function exportInvoicing(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function pmmExport(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.PmmExport,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteFlowdata(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Delete+'/'+ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function summaryFlowData(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.SummaryFlowData + `?yearMonths=${params.yearMonths}`,
    },
    {
      errorMessageMode: mode,
      isTransformResponse: false
    },
  );
}

export async function getByYearMonths(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.GetByYearMonths,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getByIdFlowData(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.GetByIdFlowData + '/' + data.id,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function splitFlowData(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.SplitFlowData,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getCommissionByYearMonths(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.GetCommissionByYearMonths,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function costExpenseFinal(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.CostExpenseFinal,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function updateFlowDataList(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.UpdateFlowDataList,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
