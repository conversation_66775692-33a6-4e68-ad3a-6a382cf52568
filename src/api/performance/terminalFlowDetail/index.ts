import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/pmm/crmZsdlFlowdata/page',
  Export = '/pmm/b2b/invoice/export',
}

/**
 * @description: 查询CttDeposit分页列表
 */
export async function getFlowPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取CttDeposit信息
 */
export async function exportInvoicing(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
