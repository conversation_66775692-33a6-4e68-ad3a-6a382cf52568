import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

/**
 * @description: CttDeposit分页参数 模型
 */
export type CttDepositPageParams = BasicPageParams;

/**
 * @description: CttDeposit分页返回值模型
 */
export interface CttDepositPageModel {
  advancedQueryConditions: string;
  combinationFormula: string;
  field: string;
  invoiceEndDate: string;
  invoiceStartDate: string;
  keyword: string;
  limit: 0;
  maraZtym: string;
  order: string;
  size: 0;
  t151tKtext: string;
  treeConditions: string;
  vbrkKunag: string;
  vbrkVbeln: string;
  vbrpCharg: string;
}

/**
 * @description: CttDeposit表类型
 */
export interface CttDepositModel {
  id: string;

  agentId: string;

  agentName: string;

  amount: number;

  realAmount: number;

  balance: number;

  createUserId: string;

  createDate: string;

  modifyUserId: string;

  modifyDate: string;

  deleteMark: number;

  enabledMark: number;

  cttDepositRecordList?: CttDepositRecordModel;
}

/**
 * @description: CttDepositRecord表类型
 */
export interface CttDepositRecordModel {
  id: string;

  depositId: string;

  agentId: string;

  agentName: string;

  contractId: string;

  contractName: string;

  dealType: string;

  dealTypeName: string;

  depositType: string;

  depositTypeName: string;

  dealRemark: string;

  approvalDeptId: string;

  approvalDeptName: string;

  approvalUserId: string;

  approvalUserName: string;

  approvalTime: string;

  amount: number;

  executionTime: string;

  executionUserId: string;

  executionUserName: string;

  createUserId: string;

  createDate: string;

  modifyUserId: string;

  modifyDate: string;

  deleteMark: number;

  enabledMark: number;
}

/**
 * @description: CttDeposit分页返回值结构
 */
export type CttDepositPageResult = BasicFetchResult<CttDepositPageModel>;
