import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Month = '/pmm/performance/month/page',
  Quarter = '/pmm/performance/quarter/page',
  Year = '/pmm/performance/year/page',
  Export = '/pmm/b2b/invoice/export',
}

export async function getMonthPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Month,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getQuarterPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Quarter,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getYearPage(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.Year,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取CttDeposit信息
 */
export async function exportInvoicing(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
