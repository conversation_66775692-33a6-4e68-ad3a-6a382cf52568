import {
  CrmTerminalLocationModel,
  CrmTerminalPageModel,
  CrmTerminalPageParams,
  CrmTerminalPageResult,
} from './model/TerminalModel';
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/crm/terminal/page',
  reservePage = '/bdm/agentterminalrel/page',
  List = '/crm/terminal/list',
  Info = '/crm/terminal/info',
  Add = '/crm/terminal/add',
  Update = '/crm/terminal/update',
  Delete = '/crm/terminal/delete',
  Export = '/crm/terminal/export',
  Enable = '/crm/terminal/enable',
  Disable = '/crm/terminal/disable',
  GetDepartmentByTerminalIdSelect = '/crm/terminal/getDepartmentByTerminalIdSelect',

  DeptPage = '/crm/terminal/dept-page',
  DeptInfo = '/crm/terminal/dept-info',
  AddDept = '/crm/terminal/dept-add',
  UpdateDept = '/crm/terminal/dept-update',
  DeleteDept = '/crm/terminal/dept-delete',
  getLocationById = '/crm/terminal/getLocationById',
  getTerminalPageByName = '/crm/terminal/getTerminalPageByName',
  getTerminalById = '/crm/terminal/getTerminalById',
}

/**
 * @description: 查询CrmTerminal分页列表
 */
export async function getCrmTerminalPage(
  params: CrmTerminalPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<CrmTerminalPageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getreserveTerminalPage(
  params: CrmTerminalPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<CrmTerminalPageResult>(
    {
      url: Api.reservePage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取CrmTerminal信息
 */
export async function getCrmTerminal(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CrmTerminalPageModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增CrmTerminal
 */
export async function addCrmTerminal(crmTerminal: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Add,
      params: crmTerminal,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新CrmTerminal
 */
export async function updateCrmTerminal(crmTerminal: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Update,
      params: crmTerminal,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除CrmTerminal（批量删除）
 */
export async function deleteCrmTerminal(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出CrmTerminal
 */
export async function exportCrmTerminal(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 根据HCO机构id获取HCO位置
 */
export async function getLocationById(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CrmTerminalLocationModel[]>(
    {
      url: Api.getLocationById,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/*
 * 启用
 */
export async function enableList(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Enable,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/*
 * 禁用
 */
export async function disableList(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Disable,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取科室
 */
export async function getDepartmentByTerminalIdSelect(
  params: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get(
    {
      url: Api.GetDepartmentByTerminalIdSelect,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

// 科室
/**
 * @description: 查询CrmTerminal分页列表
 */
export async function getCrmTerminalDeptPage(
  params: CrmTerminalPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<CrmTerminalPageResult>(
    {
      url: Api.DeptPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取CrmTerminal信息
 */
export async function getCrmTerminalDept(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CrmTerminalPageModel>(
    {
      url: Api.DeptInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getTerminalById(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.getTerminalById,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function getTerminalPageByName(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.getTerminalPageByName,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增CrmTerminal
 */
export async function addCrmTerminalDept(
  crmTerminal: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.AddDept,
      params: crmTerminal,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新CrmTerminal
 */
export async function updateCrmTerminalDept(
  crmTerminal: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.UpdateDept,
      params: crmTerminal,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除CrmTerminal（批量删除）
 */
export async function deleteCrmTerminalDept(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.DeleteDept,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
