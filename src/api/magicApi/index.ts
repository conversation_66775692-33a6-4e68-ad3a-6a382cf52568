import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
const whiteList = ['/bdm/agent/getPreAgentSelect'];
/**
 * @description: 请求magicApi
 */
export const requestMagicApi = (
  { headers, query, body, method, url },
  mode: ErrorMessageMode = 'modal',
) => {
  if (whiteList.includes(url)) {
    return defHttp.request(
      {
        url,
        params: query,
        data: body,
        method,
        headers: {
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
          ...headers,
        },
      },
      {
        errorMessageMode: mode,
      },
    );
  } else {
    return defHttp.request(
      {
        url: '/magic-api/' + url,
        params: query,
        data: body,
        method,
        headers: {
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
          ...headers,
        },
      },
      {
        errorMessageMode: mode,
      },
    );
  }

};
