# 是否开启mock
VITE_USE_MOCK = true

# 资源公共路径,需要以 / 开头和结尾
VITE_PUBLIC_PATH = /

# 是否删除Console.log
VITE_DROP_CONSOLE = true

# 打包是否输出gz｜br文件
# 可选: gzip | brotli | none
# 也可以有多个, 例如 ‘gzip’|'brotli',这样会同时生成 .gz和.br文件
VITE_BUILD_COMPRESS = 'gzip'

# 使用compress时是否删除源文件，默认false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 接口地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_API_URL=


# 文件上传地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_UPLOAD_URL = /system/oss/multi-upload

# 文件预览接口  可选
VITE_GLOB_UPLOAD_PREVIEW = http://*************:8012/onlinePreview?url=

#外部url地址
VITE_GLOB_OUT_LINK_URL = http://*************:3200

#调查问卷地址
VITE_GLOB_QN_LINK_URL = http://*************:3100

# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX = /basic-api

# 打包是否开启pwa功能
VITE_USE_PWA = false

# 是否启用官网代码
VITE_GLOB_PRODUCTION = true

# 代理商代表导入模板下载地址
VITE_GLOB_AGENT_TEMPLATE_URL = https://kszs.guoyaoplat.com/minio/precise-merch/precise/template/代理商代表导入模板.xlsx

#招商新流向导入模板
VITE_GLOB_FLOW_TEMPLATE_URL = https://kszs.guoyaoplat.com/minio/precise-merch/precise/template/招商新流向导入模板.xlsx

#招商修订向导入模板
VITE_GLOB_EDIT_TEMPLATE_URL = https://kszs.guoyaoplat.com/minio/precise-merch/precise/template/招商修订向导入模板.xlsx
