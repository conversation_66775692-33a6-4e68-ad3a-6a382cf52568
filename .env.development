# 是否开启mock数据，关闭时需要自行对接后台接口
VITE_USE_MOCK = false

# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = /

# 本地开发代理，可以解决跨域及多地址代理
# 如果接口地址匹配到，则会转发到http://localhost:3000，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
VITE_PROXY = [["/basic-api","https://test-gy-zsdl.guoyaoplat.com/basic-api"],["/upload","http://localhost:3300/upload"]]
# VITE_PROXY=[["/api","https://vvbin.cn/test"]]

# 是否删除Console.log
VITE_DROP_CONSOLE = false

# 接口地址
# 如果没有跨域问题，直接在这里配置即可
# VITE_GLOB_API_URL=http://localhost

# 文件上传接口  可选
VITE_GLOB_UPLOAD_URL = /system/oss/upload

# 文件预览接口  可选
VITE_GLOB_UPLOAD_PREVIEW = http://*************:8013/onlinePreview?url=

#外部url地址
VITE_GLOB_OUT_LINK_URL = http://localhost:4100

#调查问卷地址
VITE_GLOB_QN_LINK_URL = http://*************:3100

# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX = /basic-api

# 是否启用官网代码
VITE_GLOB_PRODUCTION = false

# 代理商代表导入模板下载地址
VITE_GLOB_AGENT_TEMPLATE_URL = https://test-gy-zsdl.guoyaoplat.com/zsdl/zsdl/template/代理商代表导入模板.xlsx
